<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Job Notification Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains the configuration for the simplified job notification 
    | system.
    |
    */

    /*
    |--------------------------------------------------------------------------
    | Default Search Radius
    |--------------------------------------------------------------------------
    |
    | The default radius (in miles) to use when searching for businesses
    | around a job's location.
    |
    */
    'default_radius' => (int) env('JOB_NOTIFICATION_DEFAULT_RADIUS', 30),

    /*
    |--------------------------------------------------------------------------
    | Maximum Search Radius
    |--------------------------------------------------------------------------
    |
    | The maximum radius (in miles) that can be used when searching for 
    | businesses.
    |
    */
    'max_radius' => (int) env('JOB_NOTIFICATION_MAX_RADIUS', 75),

    /*
    |--------------------------------------------------------------------------
    | Admin Email
    |--------------------------------------------------------------------------
    |
    | The email address of the admin who will receive job notification
    | campaign approval requests.
    |
    */
    'admin_email' => env('JOB_NOTIFICATION_ADMIN_EMAIL', '<EMAIL>'),

    /*
    |--------------------------------------------------------------------------
    | Token Expiry
    |--------------------------------------------------------------------------
    |
    | The number of hours that a notification approval token remains valid.
    |
    */
    'token_expiry_hours' => (int) env('JOB_NOTIFICATION_TOKEN_EXPIRY_HOURS', 24),

    /*
    |--------------------------------------------------------------------------
    | Business Crawler Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for the background business crawler system that searches
    | for businesses when none are initially found for a job notification.
    |
    */
    'business_crawler' => [
        /*
        | Enable or disable the business crawler system
        */
        'enabled' => env('BUSINESS_CRAWLER_ENABLED', true),

        /*
        | Maximum number of attempts to find businesses
        */
        'max_attempts' => (int) env('BUSINESS_CRAWLER_MAX_ATTEMPTS', 10),

        /*
        | Retry delays in seconds for each attempt
        | [1 minute, 5 minutes, 15 minutes, 30 minutes, 1 hour]
        */
        'retry_delays' => [60, 300, 900, 1800, 3600],

        /*
        | Maximum radius multiplier from original radius
        | If original radius is 30 miles, max will be 30 * 3 = 90 miles
        */
        'max_radius_multiplier' => (float) env('BUSINESS_CRAWLER_MAX_RADIUS_MULTIPLIER', 3.0),

        /*
        | Factor by which to expand radius on each attempt
        | 1.25 means 25% increase each time
        */
        'radius_expansion_factor' => (float) env('BUSINESS_CRAWLER_RADIUS_EXPANSION_FACTOR', 1.25),

        /*
        | Notify admin when crawler jobs fail permanently
        */
        'notify_admin_on_failure' => env('BUSINESS_CRAWLER_NOTIFY_ADMIN_ON_FAILURE', true),

        /*
        | Queue name for crawler jobs
        */
        'queue_name' => env('BUSINESS_CRAWLER_QUEUE', 'business-crawler'),
    ],
]; 