<?php

namespace App\Enums;

enum BusinessCrawlerJobStatusEnum: string
{
    case PENDING = 'pending';
    case RUNNING = 'running';
    case PAUSED = 'paused';
    case COMPLETED = 'completed';
    case FAILED = 'failed';
    case CANCELLED = 'cancelled';

    /**
     * Get human-readable label for the status
     */
    public function label(): string
    {
        return match($this) {
            self::PENDING => 'Pending',
            self::RUNNING => 'Running',
            self::PAUSED => 'Paused',
            self::COMPLETED => 'Completed',
            self::FAILED => 'Failed',
            self::CANCELLED => 'Cancelled',
        };
    }

    /**
     * Get color code for UI display
     */
    public function color(): string
    {
        return match($this) {
            self::PENDING => 'warning',
            self::RUNNING => 'primary',
            self::PAUSED => 'secondary',
            self::COMPLETED => 'success',
            self::FAILED => 'danger',
            self::CANCELLED => 'dark',
        };
    }

    /**
     * Check if status allows cancellation
     */
    public function canBeCancelled(): bool
    {
        return in_array($this, [self::PENDING, self::RUNNING, self::PAUSED]);
    }

    /**
     * Check if status allows pausing
     */
    public function canBePaused(): bool
    {
        return in_array($this, [self::PENDING, self::RUNNING]);
    }

    /**
     * Check if status allows resuming
     */
    public function canBeResumed(): bool
    {
        return $this === self::PAUSED;
    }

    /**
     * Check if status allows retry
     */
    public function canBeRetried(): bool
    {
        return in_array($this, [self::FAILED, self::CANCELLED]);
    }

    /**
     * Check if status is terminal (cannot change)
     */
    public function isTerminal(): bool
    {
        return in_array($this, [self::COMPLETED, self::FAILED, self::CANCELLED]);
    }

    /**
     * Check if status is active (can be processed)
     */
    public function isActive(): bool
    {
        return in_array($this, [self::PENDING, self::RUNNING]);
    }

    /**
     * Get all possible statuses as array
     */
    public static function values(): array
    {
        return array_column(self::cases(), 'value');
    }

    /**
     * Get statuses that are considered "in progress"
     */
    public static function inProgress(): array
    {
        return [self::PENDING->value, self::RUNNING->value, self::PAUSED->value];
    }

    /**
     * Get terminal statuses
     */
    public static function terminal(): array
    {
        return [self::COMPLETED->value, self::FAILED->value, self::CANCELLED->value];
    }
} 