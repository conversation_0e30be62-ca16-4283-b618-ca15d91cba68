<?php

namespace App\Console\Commands;

use App\Models\BusinessCrawlerJob;
use App\Services\BusinessCrawlerService;
use App\Enums\BusinessCrawlerJobStatusEnum;
use Illuminate\Console\Command;

class BusinessCrawlerStatus extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'crawler:status 
                           {--detailed : Show detailed information}
                           {--status= : Filter by status}
                           {--limit=10 : Number of jobs to show}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Show business crawler jobs status and statistics';

    /**
     * Execute the console command.
     */
    public function handle(BusinessCrawlerService $crawlerService): int
    {
        $this->info('Business Crawler Status Report');
        $this->line('=====================================');

        // Get overall statistics
        $statistics = $crawlerService->getCrawlerStatistics();
        
        $this->table(['Metric', 'Value'], [
            ['Total Jobs', $statistics['total_jobs']],
            ['Completed Jobs', $statistics['completed_jobs']],
            ['Failed Jobs', $statistics['failed_jobs']],
            ['Active Jobs', $statistics['active_jobs']],
            ['Success Rate', $statistics['success_rate'] . '%'],
            ['Total Businesses Found', $statistics['total_businesses_found']],
        ]);

        // Status breakdown
        $this->line('');
        $this->info('Status Breakdown:');
        $statusBreakdown = BusinessCrawlerJob::selectRaw('status, COUNT(*) as count')
            ->groupBy('status')
            ->get()
            ->map(function ($item) {
                return [
                    'Status' => ucfirst($item->status),
                    'Count' => $item->count,
                ];
            });
        
        $this->table(['Status', 'Count'], $statusBreakdown->toArray());

        // Show recent jobs
        $this->line('');
        $this->info('Recent Crawler Jobs:');
        
        $query = BusinessCrawlerJob::with(['campaign']);
        
        if ($this->option('status')) {
            $query->where('status', $this->option('status'));
        }
        
        $limit = $this->option('limit');
        $jobs = $query->latest()->limit($limit)->get();

        if ($jobs->isEmpty()) {
            $this->warn('No crawler jobs found.');
            return 0;
        }

        $tableData = $jobs->map(function ($job) {
            $data = [
                'ID' => $job->id,
                'Campaign' => $job->campaign_id,
                'Status' => $job->status->label(),
                'Attempts' => "{$job->attempts}/{$job->max_attempts}",
                'Businesses Found' => $job->businesses_found,
                'Created' => $job->created_at->diffForHumans(),
            ];

            if ($this->option('detailed')) {
                $data['Current Radius'] = $job->current_radius . ' miles';
                $data['Next Run'] = $job->next_run_at ? $job->next_run_at->diffForHumans() : 'N/A';
                $data['Last Error'] = $job->last_error ? \Str::limit($job->last_error, 50) : 'None';
            }

            return $data;
        });

        $this->table(array_keys($tableData->first()), $tableData->toArray());

        // Show jobs ready to run
        $readyJobs = BusinessCrawlerJob::readyToRun()->count();
        if ($readyJobs > 0) {
            $this->line('');
            $this->warn("⚡ {$readyJobs} crawler jobs are ready to run now.");
        }

        // Show failed jobs
        $failedJobs = BusinessCrawlerJob::failed()->count();
        if ($failedJobs > 0) {
            $this->line('');
            $this->error("❌ {$failedJobs} crawler jobs have failed and may need attention.");
        }

        return 0;
    }
} 