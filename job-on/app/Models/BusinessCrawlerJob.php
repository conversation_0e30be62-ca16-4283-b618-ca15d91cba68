<?php

namespace App\Models;

use App\Enums\BusinessCrawlerJobStatusEnum;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class BusinessCrawlerJob extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'campaign_id',
        'job_data',
        'search_criteria',
        'status',
        'attempts',
        'max_attempts',
        'current_radius',
        'original_radius',
        'max_radius',
        'next_run_at',
        'last_run_at',
        'businesses_found',
        'total_searches_performed',
        'last_error',
        'search_history',
        'completed_at',
        'cancelled_at',
        'cancelled_by',
        'cancellation_reason',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'job_data' => 'array',
        'search_criteria' => 'array',
        'search_history' => 'array',
        'status' => BusinessCrawlerJobStatusEnum::class,
        'attempts' => 'integer',
        'max_attempts' => 'integer',
        'current_radius' => 'decimal:2',
        'original_radius' => 'decimal:2',
        'max_radius' => 'decimal:2',
        'businesses_found' => 'integer',
        'total_searches_performed' => 'integer',
        'next_run_at' => 'datetime',
        'last_run_at' => 'datetime',
        'completed_at' => 'datetime',
        'cancelled_at' => 'datetime',
    ];

    /**
     * Get the campaign that owns this crawler job.
     */
    public function campaign(): BelongsTo
    {
        return $this->belongsTo(JobNotificationCampaign::class, 'campaign_id');
    }

    /**
     * Scope for active crawler jobs that can be processed.
     */
    public function scopeActive($query)
    {
        return $query->whereIn('status', BusinessCrawlerJobStatusEnum::inProgress());
    }

    /**
     * Scope for jobs ready to run.
     */
    public function scopeReadyToRun($query)
    {
        return $query->where('status', BusinessCrawlerJobStatusEnum::PENDING)
                    ->where(function ($q) {
                        $q->whereNull('next_run_at')
                          ->orWhere('next_run_at', '<=', now());
                    });
    }

    /**
     * Scope for failed jobs.
     */
    public function scopeFailed($query)
    {
        return $query->where('status', BusinessCrawlerJobStatusEnum::FAILED);
    }

    /**
     * Scope for completed jobs.
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', BusinessCrawlerJobStatusEnum::COMPLETED);
    }

    /**
     * Check if the job can be cancelled.
     */
    public function canBeCancelled(): bool
    {
        return $this->status->canBeCancelled();
    }

    /**
     * Check if the job can be paused.
     */
    public function canBePaused(): bool
    {
        return $this->status->canBePaused();
    }

    /**
     * Check if the job can be resumed.
     */
    public function canBeResumed(): bool
    {
        return $this->status->canBeResumed();
    }

    /**
     * Check if the job can be retried.
     */
    public function canBeRetried(): bool
    {
        return $this->status->canBeRetried();
    }

    /**
     * Check if the job has reached maximum attempts.
     */
    public function hasReachedMaxAttempts(): bool
    {
        return $this->attempts >= $this->max_attempts;
    }

    /**
     * Mark the job as running.
     */
    public function markAsRunning(): void
    {
        $this->update([
            'status' => BusinessCrawlerJobStatusEnum::RUNNING,
            'last_run_at' => now(),
        ]);
    }

    /**
     * Mark the job as completed.
     */
    public function markAsCompleted(int $businessesFound = 0): void
    {
        $this->update([
            'status' => BusinessCrawlerJobStatusEnum::COMPLETED,
            'businesses_found' => $businessesFound,
            'completed_at' => now(),
            'next_run_at' => null,
        ]);
    }

    /**
     * Mark the job as failed.
     */
    public function markAsFailed(string $error = null): void
    {
        $this->update([
            'status' => BusinessCrawlerJobStatusEnum::FAILED,
            'last_error' => $error,
            'next_run_at' => null,
        ]);
    }

    /**
     * Pause the job.
     */
    public function pause(): void
    {
        $this->update([
            'status' => BusinessCrawlerJobStatusEnum::PAUSED,
            'next_run_at' => null,
        ]);
    }

    /**
     * Resume the job.
     */
    public function resume(): void
    {
        $this->update([
            'status' => BusinessCrawlerJobStatusEnum::PENDING,
            'next_run_at' => now(),
        ]);
    }

    /**
     * Cancel the job.
     */
    public function cancel(string $reason = null, string $cancelledBy = null): void
    {
        $this->update([
            'status' => BusinessCrawlerJobStatusEnum::CANCELLED,
            'cancelled_at' => now(),
            'cancelled_by' => $cancelledBy,
            'cancellation_reason' => $reason,
            'next_run_at' => null,
        ]);
    }

    /**
     * Increment attempts and schedule next run.
     */
    public function incrementAttempt(Carbon $nextRunAt = null): void
    {
        $this->increment('attempts');
        $this->increment('total_searches_performed');

        if ($this->hasReachedMaxAttempts()) {
            $this->markAsFailed('Maximum attempts reached');
        } else {
            $this->update([
                'status' => BusinessCrawlerJobStatusEnum::PENDING,
                'next_run_at' => $nextRunAt ?? $this->calculateNextRunTime(),
            ]);
        }
    }

    /**
     * Calculate next run time based on attempt number.
     */
    public function calculateNextRunTime(): Carbon
    {
        $delays = config('job_notification.business_crawler.retry_delays', [60, 300, 900, 1800, 3600]);
        $delayIndex = min($this->attempts, count($delays) - 1);
        $delaySeconds = $delays[$delayIndex];

        return now()->addSeconds($delaySeconds);
    }

    /**
     * Update search criteria for next attempt.
     */
    public function updateSearchCriteria(array $newCriteria): void
    {
        // Store current search in history
        $history = $this->search_history ?? [];
        $history[] = [
            'attempt' => $this->attempts,
            'timestamp' => now()->toISOString(),
            'criteria' => $this->search_criteria,
            'radius' => $this->current_radius,
            'businesses_found' => 0, // Will be updated after search
        ];

        $this->update([
            'search_criteria' => $newCriteria,
            'search_history' => $history,
        ]);
    }

    /**
     * Get the job's progress percentage.
     */
    public function getProgressAttribute(): float
    {
        if ($this->status === BusinessCrawlerJobStatusEnum::COMPLETED) {
            return 100.0;
        }

        if ($this->status === BusinessCrawlerJobStatusEnum::FAILED || 
            $this->status === BusinessCrawlerJobStatusEnum::CANCELLED) {
            return 0.0;
        }

        return min(($this->attempts / $this->max_attempts) * 100, 99.0);
    }

    /**
     * Get estimated next run time in human readable format.
     */
    public function getEstimatedNextRunAttribute(): string
    {
        if (!$this->next_run_at) {
            return 'Not scheduled';
        }

        if ($this->next_run_at->isPast()) {
            return 'Ready to run';
        }

        return $this->next_run_at->diffForHumans();
    }

    /**
     * Get the duration since job creation.
     */
    public function getDurationAttribute(): string
    {
        $endTime = $this->completed_at ?? $this->cancelled_at ?? now();
        return $this->created_at->diffForHumans($endTime, true);
    }
} 