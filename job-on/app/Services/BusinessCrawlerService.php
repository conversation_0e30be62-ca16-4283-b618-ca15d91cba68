<?php

namespace App\Services;

use App\Models\BusinessCrawlerJob;
use App\Models\JobNotificationCampaign;
use App\Models\JobNotificationRecipient;
use App\Enums\JobNotificationRecipientStatusEnum;
use App\Jobs\ProcessBusinessCrawlerJob;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class BusinessCrawlerService
{
    /**
     * The BusinessDiscoveryService instance
     *
     * @var BusinessDiscoveryService
     */
    protected $businessDiscoveryService;

    /**
     * Create a new BusinessCrawlerService instance.
     *
     * @param BusinessDiscoveryService $businessDiscoveryService
     */
    public function __construct(BusinessDiscoveryService $businessDiscoveryService)
    {
        $this->businessDiscoveryService = $businessDiscoveryService;
    }

    /**
     * Create a new crawler job for a campaign with no businesses found.
     *
     * @param JobNotificationCampaign $campaign
     * @param array $originalJobData
     * @return BusinessCrawlerJob
     */
    public function createCrawlerJob(JobNotificationCampaign $campaign, array $originalJobData): BusinessCrawlerJob
    {
        $originalRadius = (float) config('job_notification.default_radius', 30);
        $maxRadius = $originalRadius * config('job_notification.business_crawler.max_radius_multiplier', 3.0);
        $maxAttempts = config('job_notification.business_crawler.max_attempts', 10);

        $searchCriteria = [
            'zip_code' => $originalJobData['zip_code'],
            'category' => $originalJobData['category_id'] ?? null,
            'latitude' => $originalJobData['latitude'] ?? null,
            'longitude' => $originalJobData['longitude'] ?? null,
            'radius' => $originalRadius,
        ];

        $crawlerJob = BusinessCrawlerJob::create([
            'campaign_id' => $campaign->id,
            'job_data' => $originalJobData,
            'search_criteria' => $searchCriteria,
            'original_radius' => $originalRadius,
            'current_radius' => $originalRadius,
            'max_radius' => $maxRadius,
            'max_attempts' => $maxAttempts,
            'next_run_at' => now(), // Run immediately
        ]);

        Log::info('Created business crawler job', [
            'crawler_job_id' => $crawlerJob->id,
            'campaign_id' => $campaign->id,
            'job_id' => $originalJobData['job_id'] ?? 'unknown',
            'zip_code' => $searchCriteria['zip_code'],
            'original_radius' => $originalRadius,
            'max_radius' => $maxRadius,
        ]);

        return $crawlerJob;
    }

    /**
     * Execute a crawler job attempt.
     *
     * @param BusinessCrawlerJob $crawlerJob
     * @return bool True if businesses were found and notifications sent
     */
    public function executeCrawlerJob(BusinessCrawlerJob $crawlerJob): bool
    {
        Log::info('Executing crawler job', [
            'crawler_job_id' => $crawlerJob->id,
            'attempt' => $crawlerJob->attempts + 1,
            'current_radius' => $crawlerJob->current_radius,
        ]);

        try {
            // Mark job as running
            $crawlerJob->markAsRunning();

            // Search for businesses with current criteria
            $businesses = $this->searchForBusinesses($crawlerJob);

            Log::info('Crawler job search completed', [
                'crawler_job_id' => $crawlerJob->id,
                'businesses_found' => $businesses->count(),
                'search_radius' => $crawlerJob->current_radius,
            ]);

            // Update search history with results
            $this->updateSearchHistory($crawlerJob, $businesses->count());

            if ($businesses->isNotEmpty()) {
                // Businesses found! Create recipients and send notifications
                $this->processFoundBusinesses($crawlerJob, $businesses);
                return true;
            } else {
                // No businesses found, prepare for retry or failure
                $this->handleNoBusinessesFound($crawlerJob);
                return false;
            }

        } catch (\Exception $e) {
            Log::error('Crawler job execution failed', [
                'crawler_job_id' => $crawlerJob->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            $crawlerJob->markAsFailed($e->getMessage());
            return false;
        }
    }

    /**
     * Search for businesses using current crawler job criteria.
     *
     * @param BusinessCrawlerJob $crawlerJob
     * @return Collection
     */
    protected function searchForBusinesses(BusinessCrawlerJob $crawlerJob): Collection
    {
        $criteria = $crawlerJob->search_criteria;

        // Use the existing BusinessDiscoveryService with current radius
        $businesses = $this->businessDiscoveryService->findBusinesses(
            $criteria['zip_code'],
            $criteria['category'],
            $crawlerJob->current_radius
        );

        // Filter out businesses that have already been notified for this campaign
        $campaign = $crawlerJob->campaign;
        $alreadyNotifiedBusinessIds = $campaign->recipients()
            ->pluck('business_id')
            ->toArray();

        return $businesses->filter(function ($business) use ($alreadyNotifiedBusinessIds) {
            return !in_array($business->id, $alreadyNotifiedBusinessIds);
        });
    }

    /**
     * Process found businesses by creating recipients and sending notifications.
     *
     * @param BusinessCrawlerJob $crawlerJob
     * @param Collection $businesses
     */
    protected function processFoundBusinesses(BusinessCrawlerJob $crawlerJob, Collection $businesses): void
    {
        $campaign = $crawlerJob->campaign;

        // Create recipient records
        foreach ($businesses as $business) {
            JobNotificationRecipient::create([
                'job_notification_campaign_id' => $campaign->id,
                'business_id' => $business->id,
                'business_name' => $business->name,
                'business_email' => $business->email,
                'business_phone' => $business->phone,
                'distance' => $business->distance,
                'status' => JobNotificationRecipientStatusEnum::PENDING,
            ]);
        }

        // Update campaign business count
        $campaign->increment('business_count', $businesses->count());

        // Mark crawler job as completed
        $crawlerJob->markAsCompleted($businesses->count());

        // Send notifications to the new businesses
        $this->sendNotificationsToNewBusinesses($campaign, $businesses);

        Log::info('Crawler job completed successfully', [
            'crawler_job_id' => $crawlerJob->id,
            'campaign_id' => $campaign->id,
            'businesses_found' => $businesses->count(),
            'total_attempts' => $crawlerJob->attempts,
        ]);
    }

    /**
     * Handle the case when no businesses are found.
     *
     * @param BusinessCrawlerJob $crawlerJob
     */
    protected function handleNoBusinessesFound(BusinessCrawlerJob $crawlerJob): void
    {
        // Expand search radius for next attempt if within limits
        $nextRadius = $this->calculateNextRadius($crawlerJob);
        
        if ($nextRadius && $nextRadius <= $crawlerJob->max_radius) {
            // Update search criteria with expanded radius
            $newCriteria = $crawlerJob->search_criteria;
            $newCriteria['radius'] = $nextRadius;
            
            $crawlerJob->update([
                'current_radius' => $nextRadius,
                'search_criteria' => $newCriteria,
            ]);

            Log::info('Expanding search radius for next attempt', [
                'crawler_job_id' => $crawlerJob->id,
                'old_radius' => $crawlerJob->current_radius,
                'new_radius' => $nextRadius,
                'max_radius' => $crawlerJob->max_radius,
            ]);
        }

        // Schedule next attempt with exponential backoff
        $crawlerJob->incrementAttempt();
    }

    /**
     * Calculate the next search radius.
     *
     * @param BusinessCrawlerJob $crawlerJob
     * @return float|null
     */
    protected function calculateNextRadius(BusinessCrawlerJob $crawlerJob): ?float
    {
        $radiusExpansionFactor = config('job_notification.business_crawler.radius_expansion_factor', 1.25);
        $nextRadius = $crawlerJob->current_radius * $radiusExpansionFactor;

        // Don't expand beyond max radius
        if ($nextRadius > $crawlerJob->max_radius) {
            return null;
        }

        return round($nextRadius, 2);
    }

    /**
     * Update search history with current attempt results.
     *
     * @param BusinessCrawlerJob $crawlerJob
     * @param int $businessesFound
     */
    protected function updateSearchHistory(BusinessCrawlerJob $crawlerJob, int $businessesFound): void
    {
        $history = $crawlerJob->search_history ?? [];
        
        // Update the latest history entry or create new one
        $currentAttempt = $crawlerJob->attempts + 1;
        $historyEntry = [
            'attempt' => $currentAttempt,
            'timestamp' => now()->toISOString(),
            'criteria' => $crawlerJob->search_criteria,
            'radius' => $crawlerJob->current_radius,
            'businesses_found' => $businessesFound,
        ];

        // Replace or add current attempt
        $found = false;
        foreach ($history as $index => $entry) {
            if ($entry['attempt'] === $currentAttempt) {
                $history[$index] = $historyEntry;
                $found = true;
                break;
            }
        }

        if (!$found) {
            $history[] = $historyEntry;
        }

        $crawlerJob->update(['search_history' => $history]);
    }

    /**
     * Send notifications to newly found businesses.
     *
     * @param JobNotificationCampaign $campaign
     * @param Collection $businesses
     */
    protected function sendNotificationsToNewBusinesses(JobNotificationCampaign $campaign, Collection $businesses): void
    {
        // Get the new recipients for these businesses
        $newRecipients = $campaign->recipients()
            ->whereIn('business_id', $businesses->pluck('id'))
            ->where('status', JobNotificationRecipientStatusEnum::PENDING)
            ->get();

        // Send notifications (using existing notification system)
        foreach ($newRecipients as $recipient) {
            try {
                \Mail::to($recipient->business_email)
                    ->send(new \App\Mail\JobNotificationMail($campaign, $recipient));
                
                $recipient->update([
                    'status' => JobNotificationRecipientStatusEnum::SENT,
                    'sent_at' => now(),
                ]);

                Log::info('Crawler notification sent to business', [
                    'campaign_id' => $campaign->id,
                    'recipient_id' => $recipient->id,
                    'business_email' => $recipient->business_email,
                ]);

            } catch (\Exception $e) {
                $recipient->update([
                    'status' => JobNotificationRecipientStatusEnum::FAILED,
                    'failed_at' => now(),
                    'failure_reason' => $e->getMessage(),
                ]);

                Log::error('Failed to send crawler notification to business', [
                    'campaign_id' => $campaign->id,
                    'recipient_id' => $recipient->id,
                    'business_email' => $recipient->business_email,
                    'error' => $e->getMessage(),
                ]);
            }
        }
    }

    /**
     * Pause a crawler job.
     *
     * @param BusinessCrawlerJob $crawlerJob
     * @param string $reason
     * @return bool
     */
    public function pauseCrawlerJob(BusinessCrawlerJob $crawlerJob, string $reason = null): bool
    {
        if (!$crawlerJob->canBePaused()) {
            return false;
        }

        $crawlerJob->pause();

        Log::info('Crawler job paused', [
            'crawler_job_id' => $crawlerJob->id,
            'reason' => $reason,
        ]);

        return true;
    }

    /**
     * Resume a paused crawler job.
     *
     * @param BusinessCrawlerJob $crawlerJob
     * @return bool
     */
    public function resumeCrawlerJob(BusinessCrawlerJob $crawlerJob): bool
    {
        if (!$crawlerJob->canBeResumed()) {
            return false;
        }

        $crawlerJob->resume();

        // Dispatch the job to run immediately
        ProcessBusinessCrawlerJob::dispatch($crawlerJob);

        Log::info('Crawler job resumed', [
            'crawler_job_id' => $crawlerJob->id,
        ]);

        return true;
    }

    /**
     * Cancel a crawler job.
     *
     * @param BusinessCrawlerJob $crawlerJob
     * @param string $reason
     * @param string $cancelledBy
     * @return bool
     */
    public function cancelCrawlerJob(BusinessCrawlerJob $crawlerJob, string $reason = null, string $cancelledBy = null): bool
    {
        if (!$crawlerJob->canBeCancelled()) {
            return false;
        }

        $crawlerJob->cancel($reason, $cancelledBy);

        Log::info('Crawler job cancelled', [
            'crawler_job_id' => $crawlerJob->id,
            'reason' => $reason,
            'cancelled_by' => $cancelledBy,
        ]);

        return true;
    }

    /**
     * Retry a failed or cancelled crawler job.
     *
     * @param BusinessCrawlerJob $crawlerJob
     * @return bool
     */
    public function retryCrawlerJob(BusinessCrawlerJob $crawlerJob): bool
    {
        if (!$crawlerJob->canBeRetried()) {
            return false;
        }

        // Reset job state for retry
        $crawlerJob->update([
            'status' => \App\Enums\BusinessCrawlerJobStatusEnum::PENDING,
            'next_run_at' => now(),
            'last_error' => null,
            'completed_at' => null,
            'cancelled_at' => null,
            'cancelled_by' => null,
            'cancellation_reason' => null,
        ]);

        // Dispatch the job to run immediately
        ProcessBusinessCrawlerJob::dispatch($crawlerJob);

        Log::info('Crawler job retried', [
            'crawler_job_id' => $crawlerJob->id,
        ]);

        return true;
    }

    /**
     * Get statistics for all crawler jobs.
     *
     * @return array
     */
    public function getCrawlerStatistics(): array
    {
        $total = BusinessCrawlerJob::count();
        $completed = BusinessCrawlerJob::completed()->count();
        $failed = BusinessCrawlerJob::failed()->count();
        $active = BusinessCrawlerJob::active()->count();

        return [
            'total_jobs' => $total,
            'completed_jobs' => $completed,
            'failed_jobs' => $failed,
            'active_jobs' => $active,
            'success_rate' => $total > 0 ? round(($completed / $total) * 100, 2) : 0,
            'total_businesses_found' => BusinessCrawlerJob::sum('businesses_found'),
        ];
    }
} 