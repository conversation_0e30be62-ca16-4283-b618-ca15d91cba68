<?php

namespace App\Jobs;

use App\Models\BusinessCrawlerJob;
use App\Services\BusinessCrawlerService;
use App\Enums\BusinessCrawlerJobStatusEnum;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class ProcessBusinessCrawlerJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * The business crawler job to process
     *
     * @var BusinessCrawlerJob
     */
    protected $crawlerJob;

    /**
     * The number of times the job may be attempted.
     *
     * @var int
     */
    public $tries = 3;

    /**
     * The number of seconds to wait before retrying the job.
     *
     * @var int
     */
    public $backoff = 60;

    /**
     * The number of seconds after which the job should timeout.
     *
     * @var int
     */
    public $timeout = 300; // 5 minutes

    /**
     * Create a new job instance.
     *
     * @param BusinessCrawlerJob $crawlerJob
     * @return void
     */
    public function __construct(BusinessCrawlerJob $crawlerJob)
    {
        $this->crawlerJob = $crawlerJob;
        
        // Use dedicated queue for crawler jobs
        $this->onQueue('business-crawler');
        
        // Add delay if job is scheduled for future
        if ($crawlerJob->next_run_at && $crawlerJob->next_run_at->isFuture()) {
            $this->delay($crawlerJob->next_run_at);
        }
    }

    /**
     * Execute the job.
     *
     * @param BusinessCrawlerService $crawlerService
     * @return void
     */
    public function handle(BusinessCrawlerService $crawlerService): void
    {
        Log::info('Processing business crawler job', [
            'crawler_job_id' => $this->crawlerJob->id,
            'campaign_id' => $this->crawlerJob->campaign_id,
            'status' => $this->crawlerJob->status->value,
            'attempts' => $this->crawlerJob->attempts,
        ]);

        // Refresh model to get latest state
        $this->crawlerJob->refresh();

        // Skip if job is no longer active (paused, cancelled, etc.)
        if (!$this->crawlerJob->status->isActive()) {
            Log::info('Skipping crawler job - not active', [
                'crawler_job_id' => $this->crawlerJob->id,
                'status' => $this->crawlerJob->status->value,
            ]);
            return;
        }

        // Check if job is ready to run
        if ($this->crawlerJob->next_run_at && $this->crawlerJob->next_run_at->isFuture()) {
            Log::info('Crawler job not ready to run yet', [
                'crawler_job_id' => $this->crawlerJob->id,
                'next_run_at' => $this->crawlerJob->next_run_at->toISOString(),
            ]);
            
            // Re-dispatch with delay
            static::dispatch($this->crawlerJob)->delay($this->crawlerJob->next_run_at);
            return;
        }

        // Check if max attempts reached
        if ($this->crawlerJob->hasReachedMaxAttempts()) {
            Log::warning('Crawler job reached maximum attempts', [
                'crawler_job_id' => $this->crawlerJob->id,
                'attempts' => $this->crawlerJob->attempts,
                'max_attempts' => $this->crawlerJob->max_attempts,
            ]);
            
            $this->crawlerJob->markAsFailed('Maximum attempts reached');
            return;
        }

        try {
            // Execute the crawler job
            $businessesFound = $crawlerService->executeCrawlerJob($this->crawlerJob);

            if ($businessesFound) {
                Log::info('Crawler job completed successfully', [
                    'crawler_job_id' => $this->crawlerJob->id,
                    'businesses_found' => $this->crawlerJob->businesses_found,
                ]);
            } else {
                // No businesses found, job will retry or fail based on service logic
                Log::info('Crawler job executed but no businesses found', [
                    'crawler_job_id' => $this->crawlerJob->id,
                    'current_attempt' => $this->crawlerJob->attempts,
                    'next_run_at' => $this->crawlerJob->next_run_at?->toISOString(),
                ]);

                // If job is still pending and has next run time, schedule next attempt
                $this->crawlerJob->refresh();
                if ($this->crawlerJob->status === BusinessCrawlerJobStatusEnum::PENDING && 
                    $this->crawlerJob->next_run_at) {
                    static::dispatch($this->crawlerJob)->delay($this->crawlerJob->next_run_at);
                }
            }

        } catch (\Exception $e) {
            Log::error('Error processing crawler job', [
                'crawler_job_id' => $this->crawlerJob->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            // Mark job as failed
            $this->crawlerJob->markAsFailed($e->getMessage());
            
            // Re-throw to trigger Laravel's retry mechanism if within queue retry limits
            throw $e;
        }
    }

    /**
     * Handle a job failure.
     *
     * @param \Throwable $exception
     * @return void
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('Business crawler job failed permanently', [
            'crawler_job_id' => $this->crawlerJob->id,
            'campaign_id' => $this->crawlerJob->campaign_id,
            'error' => $exception->getMessage(),
            'attempts' => $this->attempts,
        ]);

        // Mark the crawler job as failed if not already
        $this->crawlerJob->refresh();
        if ($this->crawlerJob->status !== BusinessCrawlerJobStatusEnum::FAILED) {
            $this->crawlerJob->markAsFailed('Queue job failed: ' . $exception->getMessage());
        }

        // Optionally send notification to admin about failed crawler job
        $this->notifyAdminOfFailure($exception);
    }

    /**
     * Notify admin of crawler job failure.
     *
     * @param \Throwable $exception
     * @return void
     */
    protected function notifyAdminOfFailure(\Throwable $exception): void
    {
        try {
            $adminEmail = config('job_notification.admin_email');
            if ($adminEmail && config('job_notification.business_crawler.notify_admin_on_failure', true)) {
                
                \Mail::to($adminEmail)->send(
                    new \App\Mail\BusinessCrawlerFailureMail($this->crawlerJob, $exception)
                );

                Log::info('Admin notified of crawler job failure', [
                    'crawler_job_id' => $this->crawlerJob->id,
                    'admin_email' => $adminEmail,
                ]);
            }
        } catch (\Exception $e) {
            Log::error('Failed to notify admin of crawler job failure', [
                'crawler_job_id' => $this->crawlerJob->id,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Get the tags that should be assigned to the job.
     *
     * @return array<int, string>
     */
    public function tags(): array
    {
        return [
            'business-crawler',
            'campaign:' . $this->crawlerJob->campaign_id,
            'crawler-job:' . $this->crawlerJob->id,
        ];
    }

    /**
     * Calculate the number of seconds to wait before retrying the job.
     *
     * @return array<int, int>
     */
    public function backoff(): array
    {
        // Exponential backoff: 1 minute, 2 minutes, 4 minutes
        return [60, 120, 240];
    }

    /**
     * Determine if the job should be retried based on the exception.
     *
     * @param \Throwable $exception
     * @return bool
     */
    public function retryUntil(): \DateTime
    {
        // Allow retries for up to 1 hour from first attempt
        return now()->addHour();
    }

    /**
     * Get the display name for the queued job.
     *
     * @return string
     */
    public function displayName(): string
    {
        return 'Business Crawler Job #' . $this->crawlerJob->id . 
               ' (Campaign #' . $this->crawlerJob->campaign_id . ')';
    }
} 