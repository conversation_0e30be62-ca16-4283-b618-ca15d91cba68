<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use App\Services\BusinessDiscoveryService;
use App\Models\JobNotificationCampaign;
use App\Models\JobNotificationRecipient;
use App\Enums\JobNotificationStatusEnum;
use App\Enums\JobNotificationRecipientStatusEnum;
use App\Mail\JobNotificationApprovalMail;
use App\Mail\JobNotificationMail;
use Illuminate\Support\Str;

class ProcessJobNotificationJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * The event data from the webhook
     *
     * @var array
     */
    protected $eventData;

    /**
     * The job data extracted from event
     *
     * @var array
     */
    protected $jobData;

    /**
     * The event ID for tracking
     *
     * @var string|null
     */
    protected $eventId;

    /**
     * The number of times the job may be attempted.
     *
     * @var int
     */
    public $tries = 3;

    /**
     * The number of seconds to wait before retrying the job.
     *
     * @var int
     */
    public $backoff = 60;

    /**
     * Create a new job instance.
     *
     * @param array $eventData
     * @return void
     */
    public function __construct(array $eventData)
    {
        $this->eventData = $eventData;
        
        // Extract and validate the job data
        $this->jobData = $eventData['job_data'] ?? [];
        
        // Store event ID for tracking
        $this->eventId = $eventData['event_id'] ?? null;

        // Validate required fields
        if (empty($this->jobData['job_id'])) {
            throw new \InvalidArgumentException('Job ID is required in event data');
        }
        
        if (empty($this->jobData['title'])) {
            throw new \InvalidArgumentException('Job title is required in event data');
        }
        
        if (empty($this->jobData['zip_code'])) {
            throw new \InvalidArgumentException('Job zip_code is required in event data');
        }
    }
    
    /**
     * Execute the job.
     */
    public function handle(BusinessDiscoveryService $businessDiscoveryService): void
    {
        Log::info('Processing job notification', [
            'job_id' => $this->jobData['job_id'],
            'job_title' => $this->jobData['title'],
            'event_id' => $this->eventId,
        ]);

        try {
            // Create a campaign record
            $campaign = $this->createCampaign();
            Log::info('Created job notification campaign', [
                'campaign_id' => $campaign->id,
                'job_id' => $this->jobData['job_id'],
            ]);

            // Find businesses within radius
            $businesses = $businessDiscoveryService->findBusinesses(
                $this->jobData['zip_code'],
                null, // Skip category filtering for now since webhook sends category_id
                (float) config('job_notification.default_radius')
            );

            // Update campaign with business count
            $campaign->business_count = $businesses->count();
            $campaign->save();

            Log::info('Found businesses for job notification', [
                'job_id' => $this->jobData['job_id'],
                'campaign_id' => $campaign->id,
                'business_count' => $campaign->business_count,
            ]);

            if ($campaign->business_count > 0) {
                // Create recipient records for each business
                $this->createRecipients($campaign, $businesses);
                
                // Send approval email to admin
                $this->sendAdminApprovalEmail($campaign);

                Log::info('Job notification processed successfully and admin approval requested', [
                    'job_id' => $this->jobData['job_id'],
                    'campaign_id' => $campaign->id,
                    'business_count' => $campaign->business_count,
                ]);
            } else {
                Log::warning('No businesses found for job notification', [
                    'job_id' => $this->jobData['job_id'],
                    'zip_code' => $this->jobData['zip_code'],
                    'campaign_id' => $campaign->id,
                ]);
                
                // Check if business crawler is enabled
                if (config('job_notification.business_crawler.enabled', true)) {
                    Log::info('Creating business crawler job for campaign with no businesses found', [
                        'campaign_id' => $campaign->id,
                        'job_id' => $this->jobData['job_id'],
                    ]);
                    
                    // Create a crawler job to search for businesses in background
                    $crawlerService = app(\App\Services\BusinessCrawlerService::class);
                    $crawlerJob = $crawlerService->createCrawlerJob($campaign, $this->jobData);
                    
                    // Dispatch the crawler job to queue
                    \App\Jobs\ProcessBusinessCrawlerJob::dispatch($crawlerJob);
                    
                    // Update campaign status to indicate crawler is running
                    $campaign->status = JobNotificationStatusEnum::PENDING;
                    $campaign->rejection_reason = null;
                    $campaign->save();
                    
                    Log::info('Business crawler job dispatched', [
                        'campaign_id' => $campaign->id,
                        'crawler_job_id' => $crawlerJob->id,
                    ]);
                } else {
                    // Business crawler disabled, mark as rejected
                    $campaign->status = JobNotificationStatusEnum::REJECTED;
                    $campaign->rejection_reason = 'No businesses found in the specified area';
                    $campaign->save();
                }
            }
        } catch (\Exception $e) {
            Log::error('Error processing job notification', [
                'job_id' => $this->jobData['job_id'] ?? 'unknown',
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            throw $e;
        }
    }

    /**
     * Create a campaign record
     */
    private function createCampaign(): JobNotificationCampaign
    {
        return JobNotificationCampaign::create([
            'job_id' => $this->jobData['job_id'],
            'job_title' => $this->jobData['title'],
            'job_description' => $this->jobData['description'] ?? null,
            'job_budget' => $this->jobData['budget'] ?? $this->jobData['budget_max'] ?? $this->jobData['budget_min'] ?? null,
            'job_zip_code' => $this->jobData['zip_code'],
            'job_address' => $this->jobData['address'] ?? null,
            'job_latitude' => $this->jobData['latitude'] ?? null,
            'job_longitude' => $this->jobData['longitude'] ?? null,
            'job_category' => $this->jobData['category_id'] ?? null,
            'customer_name' => $this->jobData['customer_name'] ?? null,
            'customer_email' => $this->jobData['customer_email'] ?? null,
            'customer_phone' => $this->jobData['customer_phone'] ?? null,
            'search_radius' => config('job_notification.default_radius'),
            'status' => JobNotificationStatusEnum::PENDING,
            'admin_token' => Str::random(64),
            'token_expires_at' => now()->addHours(config('job_notification.token_expiry_hours', 24)),
            'event_id' => $this->eventId ?? null,
        ]);
    }

    /**
     * Create recipient records for each business
     */
    private function createRecipients(JobNotificationCampaign $campaign, $businesses): void
    {
        foreach ($businesses as $business) {
            JobNotificationRecipient::create([
                'job_notification_campaign_id' => $campaign->id,
                'business_id' => $business->id,
                'business_name' => $business->name,
                'business_email' => $business->email,
                'business_phone' => $business->phone,
                'distance' => $business->distance,
                'status' => JobNotificationRecipientStatusEnum::PENDING,
            ]);
        }

        Log::info('Created recipients for campaign', [
            'campaign_id' => $campaign->id,
            'recipient_count' => $businesses->count(),
        ]);
    }

    /**
     * Send admin approval email
     */
    private function sendAdminApprovalEmail(JobNotificationCampaign $campaign)
    {
        try {
            $adminEmail = config('job_notification.admin_email');
            
            Mail::to($adminEmail)->send(new JobNotificationApprovalMail($campaign, $campaign->admin_token));
            
            Log::info('Admin approval email sent', [
                'campaign_id' => $campaign->id,
                'admin_email' => $adminEmail,
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to send admin approval email', [
                'campaign_id' => $campaign->id,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Send notifications to all recipients (called after admin approval)
     */
    public static function sendNotifications(JobNotificationCampaign $campaign): void
    {
        $recipients = $campaign->recipients()->where('status', JobNotificationRecipientStatusEnum::PENDING)->get();

        foreach ($recipients as $recipient) {
            try {
                Mail::to($recipient->business_email)->send(new JobNotificationMail($campaign, $recipient));
                
                $recipient->update([
                    'status' => JobNotificationRecipientStatusEnum::SENT,
                    'sent_at' => now(),
                ]);

                Log::info('Notification sent to business', [
                    'campaign_id' => $campaign->id,
                    'recipient_id' => $recipient->id,
                    'business_email' => $recipient->business_email,
                ]);
            } catch (\Exception $e) {
                $recipient->update([
                    'status' => JobNotificationRecipientStatusEnum::FAILED,
                    'failed_at' => now(),
                    'failure_reason' => $e->getMessage(),
                ]);

                Log::error('Failed to send notification to business', [
                    'campaign_id' => $campaign->id,
                    'recipient_id' => $recipient->id,
                    'business_email' => $recipient->business_email,
                    'error' => $e->getMessage(),
                ]);
            }
        }

        // Update campaign status
        $campaign->update([
            'status' => JobNotificationStatusEnum::SENT,
            'sent_at' => now(),
        ]);

        Log::info('Campaign notifications completed', [
            'campaign_id' => $campaign->id,
            'total_recipients' => $recipients->count(),
        ]);
    }
} 