<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class BusinessCrawlerJobResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'campaign_id' => $this->campaign_id,
            'status' => [
                'value' => $this->status->value,
                'label' => $this->status->label(),
                'color' => $this->status->color(),
            ],
            'attempts' => $this->attempts,
            'max_attempts' => $this->max_attempts,
            'progress' => round($this->progress, 1),
            
            // Search criteria and radius information
            'search_criteria' => $this->search_criteria,
            'radius' => [
                'original' => $this->original_radius,
                'current' => $this->current_radius,
                'max' => $this->max_radius,
            ],
            
            // Timing information
            'timing' => [
                'created_at' => $this->created_at,
                'last_run_at' => $this->last_run_at,
                'next_run_at' => $this->next_run_at,
                'estimated_next_run' => $this->estimated_next_run,
                'completed_at' => $this->completed_at,
                'cancelled_at' => $this->cancelled_at,
                'duration' => $this->duration,
            ],
            
            // Results and progress
            'results' => [
                'businesses_found' => $this->businesses_found,
                'total_searches_performed' => $this->total_searches_performed,
                'search_history' => $this->when(
                    $request->include_search_history || $request->detailed, 
                    $this->search_history
                ),
            ],
            
            // Error information
            'error' => [
                'last_error' => $this->last_error,
                'cancellation_reason' => $this->cancellation_reason,
                'cancelled_by' => $this->cancelled_by,
            ],
            
            // Action permissions
            'actions' => [
                'can_pause' => $this->canBePaused(),
                'can_resume' => $this->canBeResumed(),
                'can_cancel' => $this->canBeCancelled(),
                'can_retry' => $this->canBeRetried(),
            ],
            
            // Related campaign information
            'campaign' => $this->when(
                $this->relationLoaded('campaign'),
                function () {
                    return [
                        'id' => $this->campaign->id,
                        'job_id' => $this->campaign->job_id,
                        'job_title' => $this->campaign->job_title,
                        'job_zip_code' => $this->campaign->job_zip_code,
                        'status' => $this->campaign->status,
                        'business_count' => $this->campaign->business_count,
                    ];
                }
            ),
            
            // Original job data (when requested)
            'job_data' => $this->when(
                $request->include_job_data || $request->detailed,
                $this->job_data
            ),
            
            'updated_at' => $this->updated_at,
        ];
    }

    /**
     * Get additional data that should be returned with the resource array.
     *
     * @param Request $request
     * @return array<string, mixed>
     */
    public function with(Request $request): array
    {
        return [
            'meta' => [
                'status_options' => collect(\App\Enums\BusinessCrawlerJobStatusEnum::cases())
                    ->map(function ($status) {
                        return [
                            'value' => $status->value,
                            'label' => $status->label(),
                            'color' => $status->color(),
                        ];
                    }),
            ],
        ];
    }
} 