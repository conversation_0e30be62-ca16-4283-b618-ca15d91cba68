<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\BusinessCrawlerJob;
use App\Services\BusinessCrawlerService;
use App\Http\Resources\BusinessCrawlerJobResource;
use App\Enums\BusinessCrawlerJobStatusEnum;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\Rule;

class BusinessCrawlerJobController extends Controller
{
    /**
     * The BusinessCrawlerService instance
     *
     * @var BusinessCrawlerService
     */
    protected $crawlerService;

    /**
     * Create a new controller instance.
     *
     * @param BusinessCrawlerService $crawlerService
     */
    public function __construct(BusinessCrawlerService $crawlerService)
    {
        $this->crawlerService = $crawlerService;
    }

    /**
     * Get list of business crawler jobs with filtering and pagination.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        $request->validate([
            'status' => ['nullable', 'string', Rule::in(BusinessCrawlerJobStatusEnum::values())],
            'campaign_id' => 'nullable|integer|exists:job_notification_campaigns,id',
            'per_page' => 'nullable|integer|min:1|max:100',
            'page' => 'nullable|integer|min:1',
            'sort_by' => 'nullable|string|in:created_at,updated_at,next_run_at,attempts',
            'sort_order' => 'nullable|string|in:asc,desc',
        ]);

        $query = BusinessCrawlerJob::with(['campaign']);

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by campaign
        if ($request->filled('campaign_id')) {
            $query->where('campaign_id', $request->campaign_id);
        }

        // Sorting
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');
        $query->orderBy($sortBy, $sortOrder);

        // Pagination
        $perPage = $request->get('per_page', 15);
        $crawlerJobs = $query->paginate($perPage);

        return response()->json([
            'success' => true,
            'data' => BusinessCrawlerJobResource::collection($crawlerJobs),
            'meta' => [
                'pagination' => [
                    'current_page' => $crawlerJobs->currentPage(),
                    'from' => $crawlerJobs->firstItem(),
                    'last_page' => $crawlerJobs->lastPage(),
                    'per_page' => $crawlerJobs->perPage(),
                    'to' => $crawlerJobs->lastItem(),
                    'total' => $crawlerJobs->total(),
                ],
                'statistics' => $this->crawlerService->getCrawlerStatistics(),
            ]
        ]);
    }

    /**
     * Get details of a specific business crawler job.
     *
     * @param BusinessCrawlerJob $crawlerJob
     * @return JsonResponse
     */
    public function show(BusinessCrawlerJob $crawlerJob): JsonResponse
    {
        $crawlerJob->load(['campaign']);

        return response()->json([
            'success' => true,
            'data' => new BusinessCrawlerJobResource($crawlerJob)
        ]);
    }

    /**
     * Pause a business crawler job.
     *
     * @param Request $request
     * @param BusinessCrawlerJob $crawlerJob
     * @return JsonResponse
     */
    public function pause(Request $request, BusinessCrawlerJob $crawlerJob): JsonResponse
    {
        $request->validate([
            'reason' => 'nullable|string|max:500',
        ]);

        if (!$crawlerJob->canBePaused()) {
            return response()->json([
                'success' => false,
                'message' => 'Cannot pause crawler job in current status: ' . $crawlerJob->status->label(),
                'code' => 'INVALID_STATUS_TRANSITION'
            ], 400);
        }

        $success = $this->crawlerService->pauseCrawlerJob($crawlerJob, $request->reason);

        if ($success) {
            Log::info('Crawler job paused via API', [
                'crawler_job_id' => $crawlerJob->id,
                'user_id' => auth()->id(),
                'reason' => $request->reason,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Crawler job paused successfully',
                'data' => new BusinessCrawlerJobResource($crawlerJob->fresh())
            ]);
        }

        return response()->json([
            'success' => false,
            'message' => 'Failed to pause crawler job',
            'code' => 'PAUSE_FAILED'
        ], 500);
    }

    /**
     * Resume a paused business crawler job.
     *
     * @param BusinessCrawlerJob $crawlerJob
     * @return JsonResponse
     */
    public function resume(BusinessCrawlerJob $crawlerJob): JsonResponse
    {
        if (!$crawlerJob->canBeResumed()) {
            return response()->json([
                'success' => false,
                'message' => 'Cannot resume crawler job in current status: ' . $crawlerJob->status->label(),
                'code' => 'INVALID_STATUS_TRANSITION'
            ], 400);
        }

        $success = $this->crawlerService->resumeCrawlerJob($crawlerJob);

        if ($success) {
            Log::info('Crawler job resumed via API', [
                'crawler_job_id' => $crawlerJob->id,
                'user_id' => auth()->id(),
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Crawler job resumed successfully',
                'data' => new BusinessCrawlerJobResource($crawlerJob->fresh())
            ]);
        }

        return response()->json([
            'success' => false,
            'message' => 'Failed to resume crawler job',
            'code' => 'RESUME_FAILED'
        ], 500);
    }

    /**
     * Cancel a business crawler job.
     *
     * @param Request $request
     * @param BusinessCrawlerJob $crawlerJob
     * @return JsonResponse
     */
    public function cancel(Request $request, BusinessCrawlerJob $crawlerJob): JsonResponse
    {
        $request->validate([
            'reason' => 'required|string|max:500',
        ]);

        if (!$crawlerJob->canBeCancelled()) {
            return response()->json([
                'success' => false,
                'message' => 'Cannot cancel crawler job in current status: ' . $crawlerJob->status->label(),
                'code' => 'INVALID_STATUS_TRANSITION'
            ], 400);
        }

        $cancelledBy = auth()->user()->name ?? 'API User';
        $success = $this->crawlerService->cancelCrawlerJob($crawlerJob, $request->reason, $cancelledBy);

        if ($success) {
            Log::info('Crawler job cancelled via API', [
                'crawler_job_id' => $crawlerJob->id,
                'user_id' => auth()->id(),
                'reason' => $request->reason,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Crawler job cancelled successfully',
                'data' => new BusinessCrawlerJobResource($crawlerJob->fresh())
            ]);
        }

        return response()->json([
            'success' => false,
            'message' => 'Failed to cancel crawler job',
            'code' => 'CANCEL_FAILED'
        ], 500);
    }

    /**
     * Retry a failed or cancelled business crawler job.
     *
     * @param BusinessCrawlerJob $crawlerJob
     * @return JsonResponse
     */
    public function retry(BusinessCrawlerJob $crawlerJob): JsonResponse
    {
        if (!$crawlerJob->canBeRetried()) {
            return response()->json([
                'success' => false,
                'message' => 'Cannot retry crawler job in current status: ' . $crawlerJob->status->label(),
                'code' => 'INVALID_STATUS_TRANSITION'
            ], 400);
        }

        $success = $this->crawlerService->retryCrawlerJob($crawlerJob);

        if ($success) {
            Log::info('Crawler job retried via API', [
                'crawler_job_id' => $crawlerJob->id,
                'user_id' => auth()->id(),
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Crawler job retried successfully',
                'data' => new BusinessCrawlerJobResource($crawlerJob->fresh())
            ]);
        }

        return response()->json([
            'success' => false,
            'message' => 'Failed to retry crawler job',
            'code' => 'RETRY_FAILED'
        ], 500);
    }

    /**
     * Get statistics for business crawler jobs.
     *
     * @return JsonResponse
     */
    public function statistics(): JsonResponse
    {
        $statistics = $this->crawlerService->getCrawlerStatistics();

        // Add additional breakdown by status
        $statusBreakdown = BusinessCrawlerJob::selectRaw('status, COUNT(*) as count')
            ->groupBy('status')
            ->pluck('count', 'status')
            ->toArray();

        // Add recent activity
        $recentJobs = BusinessCrawlerJob::with(['campaign'])
            ->latest()
            ->limit(10)
            ->get();

        return response()->json([
            'success' => true,
            'data' => [
                'overview' => $statistics,
                'status_breakdown' => $statusBreakdown,
                'recent_jobs' => BusinessCrawlerJobResource::collection($recentJobs),
            ]
        ]);
    }

    /**
     * Get crawler jobs that are ready to run.
     *
     * @return JsonResponse
     */
    public function readyToRun(): JsonResponse
    {
        $readyJobs = BusinessCrawlerJob::readyToRun()
            ->with(['campaign'])
            ->orderBy('next_run_at')
            ->limit(50)
            ->get();

        return response()->json([
            'success' => true,
            'data' => BusinessCrawlerJobResource::collection($readyJobs),
            'meta' => [
                'total_ready' => $readyJobs->count(),
            ]
        ]);
    }

    /**
     * Bulk operations on crawler jobs.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function bulkAction(Request $request): JsonResponse
    {
        $request->validate([
            'action' => 'required|string|in:pause,resume,cancel,retry',
            'crawler_job_ids' => 'required|array|min:1|max:50',
            'crawler_job_ids.*' => 'integer|exists:business_crawler_jobs,id',
            'reason' => 'required_if:action,pause,cancel|string|max:500',
        ]);

        $crawlerJobs = BusinessCrawlerJob::whereIn('id', $request->crawler_job_ids)->get();
        $results = [];
        $successCount = 0;

        foreach ($crawlerJobs as $crawlerJob) {
            $success = false;
            $message = '';

            try {
                switch ($request->action) {
                    case 'pause':
                        $success = $this->crawlerService->pauseCrawlerJob($crawlerJob, $request->reason);
                        $message = $success ? 'Paused successfully' : 'Cannot pause in current status';
                        break;

                    case 'resume':
                        $success = $this->crawlerService->resumeCrawlerJob($crawlerJob);
                        $message = $success ? 'Resumed successfully' : 'Cannot resume in current status';
                        break;

                    case 'cancel':
                        $cancelledBy = auth()->user()->name ?? 'API User';
                        $success = $this->crawlerService->cancelCrawlerJob($crawlerJob, $request->reason, $cancelledBy);
                        $message = $success ? 'Cancelled successfully' : 'Cannot cancel in current status';
                        break;

                    case 'retry':
                        $success = $this->crawlerService->retryCrawlerJob($crawlerJob);
                        $message = $success ? 'Retried successfully' : 'Cannot retry in current status';
                        break;
                }

                if ($success) {
                    $successCount++;
                }

            } catch (\Exception $e) {
                $success = false;
                $message = 'Error: ' . $e->getMessage();
            }

            $results[] = [
                'crawler_job_id' => $crawlerJob->id,
                'success' => $success,
                'message' => $message,
            ];
        }

        Log::info('Bulk crawler job action performed', [
            'action' => $request->action,
            'total_jobs' => count($crawlerJobs),
            'successful_jobs' => $successCount,
            'user_id' => auth()->id(),
        ]);

        return response()->json([
            'success' => true,
            'message' => "Bulk {$request->action} completed. {$successCount} of " . count($crawlerJobs) . " jobs processed successfully.",
            'data' => [
                'action' => $request->action,
                'total_jobs' => count($crawlerJobs),
                'successful_jobs' => $successCount,
                'results' => $results,
            ]
        ]);
    }
} 