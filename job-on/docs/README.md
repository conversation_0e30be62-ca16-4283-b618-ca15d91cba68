# JobON Application Documentation

Welcome to the comprehensive documentation for the JobON Laravel application. This documentation provides detailed information about the system architecture, components, workflows, and development guidelines.

## 📋 Table of Contents

### Architecture Documentation
- [**Architecture Overview**](./architecture/README.md) - High-level system architecture and design patterns
- [**Application Layer**](./architecture/application-layer.md) - Controllers, Middleware, Requests, Resources
- [**Domain Layer**](./architecture/domain-layer.md) - Models, Policies, Enums, Events/Listeners
- [**Infrastructure Layer**](./architecture/infrastructure-layer.md) - Repositories, Services, External Integrations
- [**Presentation Layer**](./architecture/presentation-layer.md) - Views, Frontend Assets, API Routes
- [**Module Layer**](./architecture/module-layer.md) - Nwidart Modules documentation
- [**Database Layer**](./architecture/database-layer.md) - Migrations, Seeders, Factories
- [**Testing Layer**](./architecture/testing-layer.md) - Feature/Unit Tests

### API Documentation
- [**API Overview**](./api/README.md) - Complete API reference and guidelines
- [**Authentication**](./api/authentication.md) - JWT authentication and authorization
- [**Endpoints**](./api/endpoints.md) - Detailed endpoint documentation
- [**Resources**](./api/resources.md) - API resource transformations
- [**Error Handling**](./api/error-handling.md) - Error responses and codes

### Business Logic & Workflows
- [**Job Booking Workflow**](./workflows/job-booking.md) - Complete job booking process
- [**Bid Management**](./workflows/bid-management.md) - Bidding system workflow
- [**Notification System**](./workflows/notification-system.md) - Job notification campaigns
- [**Payment Processing**](./workflows/payment-processing.md) - Payment integration workflows
- [**User Management**](./workflows/user-management.md) - User roles and permissions

### Development Guidelines
- [**Setup & Installation**](./development/setup.md) - Local development setup
- [**Coding Standards**](./development/coding-standards.md) - PHP and Laravel standards
- [**Testing Guidelines**](./development/testing.md) - Testing best practices
- [**Deployment**](./development/deployment.md) - Production deployment guide

## 🏗️ System Overview

JobON is a comprehensive Laravel 11 application that facilitates job booking and service provider management. The system follows a modular architecture with clear separation of concerns across multiple layers.

### Key Features
- **Multi-role System**: Admin, Provider, Consumer, Supreme Admin
- **Job Booking System**: Complete job posting and bidding workflow
- **Notification Campaigns**: Automated job notification system
- **Payment Integration**: Multiple payment gateways (Stripe, PayPal, RazorPay, etc.)
- **Real-time Features**: WebSocket support for live updates
- **Modular Architecture**: Extensible module system using Nwidart

### Technology Stack
- **Backend**: Laravel 11, PHP 8.1+
- **Database**: MySQL with spatial data support
- **Frontend**: Blade templates, jQuery, Bootstrap 5, Vite
- **Authentication**: JWT for API, Session for web
- **Real-time**: WebSocket (Ratchet)
- **Testing**: PHPUnit, Pest
- **Modules**: Nwidart Laravel Modules

## 🚀 Quick Start

1. **Read the Architecture Overview** to understand the system design
2. **Check the API Documentation** for endpoint details
3. **Review Workflows** to understand business logic
4. **Follow Development Guidelines** for contributing

## 📖 Documentation Standards

This documentation follows these principles:
- **Comprehensive**: Covers all aspects of the system
- **Practical**: Includes real code examples and patterns
- **Maintainable**: Updated with code changes
- **Searchable**: Well-organized with clear navigation

## 🔄 Keeping Documentation Updated

When making changes to the codebase:
1. Update relevant documentation sections
2. Add new patterns to architecture docs
3. Update API documentation for endpoint changes
4. Document new workflows or business logic changes

## 📞 Support

For questions about this documentation or the JobON system:
- Review the specific layer documentation
- Check the workflows for business logic questions
- Refer to the API documentation for integration details
- Follow the development guidelines for contribution standards

---

*This documentation is maintained alongside the JobON codebase and reflects the current system architecture and implementation patterns.*
