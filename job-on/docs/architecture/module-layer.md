# Module Layer Documentation

## 🎯 Overview

The Module Layer implements the Nwidart Laravel Modules package to provide a modular architecture for extending JobON functionality. Each module is self-contained with its own controllers, models, routes, views, and configurations.

## 📁 Module Structure

### **Standard Module Directory Structure**
```
Modules/
├── ModuleName/
│   ├── Config/
│   │   └── config.php
│   ├── Console/
│   │   └── Commands/
│   ├── Database/
│   │   ├── Migrations/
│   │   ├── Seeders/
│   │   └── Factories/
│   ├── Entities/
│   │   └── ModelName.php
│   ├── Http/
│   │   ├── Controllers/
│   │   ├── Middleware/
│   │   └── Requests/
│   ├── Providers/
│   │   ├── ModuleNameServiceProvider.php
│   │   └── RouteServiceProvider.php
│   ├── Resources/
│   │   ├── assets/
│   │   ├── lang/
│   │   └── views/
│   ├── Routes/
│   │   ├── api.php
│   │   └── web.php
│   ├── Tests/
│   │   ├── Feature/
│   │   └── Unit/
│   ├── composer.json
│   └── module.json
```

## 🔧 Existing Modules

### **Payment Modules**

#### **Stripe Module** (`Modules/Stripe/`)
Handles Stripe payment processing integration.

**Key Components:**
- `StripeController` - Payment processing endpoints
- `StripeService` - Stripe API integration
- `StripeWebhookController` - Webhook handling
- Configuration for API keys and webhook secrets

**Features:**
- Credit card payments
- Subscription billing
- Webhook event processing
- Refund handling

#### **PayPal Module** (`Modules/PayPal/`)
PayPal payment gateway integration.

**Key Components:**
- `PayPalController` - PayPal payment flow
- `PayPalService` - PayPal API client
- Express checkout implementation
- IPN (Instant Payment Notification) handling

#### **RazorPay Module** (`Modules/RazorPay/`)
Indian payment gateway integration.

**Features:**
- UPI payments
- Net banking
- Card payments
- Wallet integrations

### **Communication Modules**

#### **Twilio Module** (`Modules/Twilio/`)
SMS and voice communication services.

**Key Components:**
- `TwilioService` - SMS sending service
- `TwilioController` - Communication endpoints
- Phone number verification
- SMS templates and localization

#### **Nexmo Module** (`Modules/Nexmo/`)
Alternative SMS service provider.

**Features:**
- International SMS
- Number verification
- Voice calls
- Message delivery tracking

#### **Firebase Module** (`Modules/Firebase/`)
Push notification and real-time features.

**Key Components:**
- `FirebaseService` - FCM integration
- Push notification templates
- Real-time database sync
- Analytics integration

### **Subscription Module** (`Modules/Subscription/`)

Manages provider subscription plans and billing.

**Key Models:**
```php
// Plan.php
class Plan extends Model
{
    protected $fillable = [
        'name',
        'max_services',
        'max_addresses', 
        'max_servicemen',
        'max_service_packages',
        'price',
        'duration', // monthly/yearly
        'description',
        'status'
    ];
}

// UserSubscription.php  
class UserSubscription extends Model
{
    protected $fillable = [
        'user_id',
        'plan_id',
        'starts_at',
        'ends_at',
        'status',
        'payment_method',
        'amount_paid'
    ];
}
```

**Features:**
- Plan management
- Subscription lifecycle
- Usage tracking
- Billing integration
- Plan upgrades/downgrades

## 🏗️ Module Service Provider Pattern

### **Base Service Provider Structure**
```php
<?php

namespace Modules\ModuleName\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Blade;
use Nwidart\Modules\Traits\PathNamespace;

class ModuleNameServiceProvider extends ServiceProvider
{
    use PathNamespace;

    protected string $name = 'ModuleName';
    protected string $nameLower = 'modulename';

    public function boot(): void
    {
        $this->registerCommands();
        $this->registerCommandSchedules();
        $this->registerTranslations();
        $this->registerConfig();
        $this->registerViews();
        $this->loadMigrationsFrom(module_path($this->name, 'Database/Migrations'));
    }

    public function register(): void
    {
        $this->app->register(EventServiceProvider::class);
        $this->app->register(RouteServiceProvider::class);
    }

    protected function registerConfig(): void
    {
        $configPath = module_path($this->name, 'Config/config.php');
        
        $this->publishes([$configPath => config_path($this->nameLower . '.php')], 'config');
        $this->mergeConfigFrom($configPath, $this->nameLower);
    }

    public function registerViews(): void
    {
        $viewPath = resource_path('views/modules/' . $this->nameLower);
        $sourcePath = module_path($this->name, 'Resources/views');

        $this->publishes([$sourcePath => $viewPath], ['views', $this->nameLower . '-module-views']);
        $this->loadViewsFrom([$sourcePath], $this->nameLower);

        Blade::componentNamespace('Modules\\' . $this->name . '\\View\\Components', $this->nameLower);
    }

    public function registerTranslations(): void
    {
        $langPath = resource_path('lang/modules/' . $this->nameLower);

        if (is_dir($langPath)) {
            $this->loadTranslationsFrom($langPath, $this->nameLower);
            $this->loadJsonTranslationsFrom($langPath);
        } else {
            $this->loadTranslationsFrom(module_path($this->name, 'Resources/lang'), $this->nameLower);
            $this->loadJsonTranslationsFrom(module_path($this->name, 'Resources/lang'));
        }
    }
}
```

### **Route Service Provider Pattern**
```php
<?php

namespace Modules\ModuleName\Providers;

use Illuminate\Foundation\Support\Providers\RouteServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Route;

class RouteServiceProvider extends ServiceProvider
{
    protected string $name = 'ModuleName';

    public function boot(): void
    {
        parent::boot();
    }

    public function map(): void
    {
        $this->mapApiRoutes();
        $this->mapWebRoutes();
    }

    protected function mapWebRoutes(): void
    {
        Route::middleware('web')
            ->group(module_path($this->name, '/Routes/web.php'));
    }

    protected function mapApiRoutes(): void
    {
        Route::middleware('api')
            ->prefix('api')
            ->name('api.')
            ->group(module_path($this->name, '/Routes/api.php'));
    }
}
```

## 🔧 Module Configuration

### **module.json Structure**
```json
{
    "name": "ModuleName",
    "alias": "modulename",
    "description": "Module description",
    "keywords": ["keyword1", "keyword2"],
    "priority": 0,
    "providers": [
        "Modules\\ModuleName\\Providers\\ModuleNameServiceProvider"
    ],
    "files": []
}
```

### **Module Status Management**
```json
// modules_statuses.json
{
    "Stripe": true,
    "PayPal": true,
    "RazorPay": false,
    "Twilio": true,
    "Firebase": true,
    "Subscription": true
}
```

## 🎯 Module Development Patterns

### **Payment Module Interface**
```php
interface PaymentGatewayInterface
{
    public function createPayment(array $data): PaymentResult;
    public function processWebhook(Request $request): WebhookResult;
    public function refundPayment(string $paymentId, float $amount): RefundResult;
    public function getPaymentStatus(string $paymentId): PaymentStatus;
}
```

### **SMS Module Interface**
```php
interface SMSProviderInterface
{
    public function sendSMS(string $to, string $message): SMSResult;
    public function verifyNumber(string $number): VerificationResult;
    public function getDeliveryStatus(string $messageId): DeliveryStatus;
}
```

### **Module Service Registration**
```php
// In module service provider
public function register(): void
{
    $this->app->bind(PaymentGatewayInterface::class, function ($app) {
        $gateway = config('payment.default_gateway');
        
        return match($gateway) {
            'stripe' => new StripePaymentGateway(),
            'paypal' => new PayPalPaymentGateway(),
            'razorpay' => new RazorPayPaymentGateway(),
            default => throw new InvalidArgumentException("Unsupported gateway: {$gateway}")
        };
    });
}
```

## 🔄 Module Lifecycle

### **Module Installation**
1. Copy module to `Modules/` directory
2. Run `php artisan module:migrate ModuleName`
3. Publish module assets: `php artisan module:publish ModuleName`
4. Enable module: `php artisan module:enable ModuleName`

### **Module Development Commands**
```bash
# Create new module
php artisan module:make ModuleName

# Generate module components
php artisan module:make-controller ModuleName ControllerName
php artisan module:make-model ModuleName ModelName
php artisan module:make-migration ModuleName create_table_name

# Module management
php artisan module:list
php artisan module:enable ModuleName
php artisan module:disable ModuleName
php artisan module:migrate ModuleName
php artisan module:seed ModuleName
```

## 🧪 Module Testing

### **Module Test Structure**
```php
// Tests/Feature/PaymentTest.php
class PaymentTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        $this->app->register(StripeServiceProvider::class);
    }

    /** @test */
    public function it_can_process_stripe_payment()
    {
        $paymentData = [
            'amount' => 100.00,
            'currency' => 'USD',
            'token' => 'tok_visa'
        ];

        $result = app(PaymentGatewayInterface::class)->createPayment($paymentData);

        $this->assertTrue($result->isSuccessful());
    }
}
```

## 📊 Module Integration Patterns

### **Event-Driven Integration**
```php
// Module listens to core events
class PaymentEventListener
{
    public function handle(JobCompleted $event)
    {
        // Process payment when job is completed
        $this->paymentService->processJobPayment($event->job);
    }
}
```

### **Service Container Integration**
```php
// Core app can resolve module services
$smsService = app(SMSProviderInterface::class);
$smsService->sendSMS($phone, $message);
```

### **Configuration Integration**
```php
// Module configs merge with core config
// config/payment.php
return [
    'default_gateway' => env('PAYMENT_GATEWAY', 'stripe'),
    'gateways' => [
        'stripe' => [
            'key' => env('STRIPE_KEY'),
            'secret' => env('STRIPE_SECRET'),
        ],
        'paypal' => [
            'client_id' => env('PAYPAL_CLIENT_ID'),
            'secret' => env('PAYPAL_SECRET'),
        ]
    ]
];
```

## 🔒 Module Security

### **Module Isolation**
- Each module has its own namespace
- Module dependencies are explicit
- Configuration is isolated
- Database migrations are module-specific

### **Permission Integration**
```php
// Module permissions integrate with core RBAC
Gate::define('manage-subscriptions', function ($user) {
    return $user->hasRole(['admin', 'supreme_admin']);
});
```

---

*The Module Layer provides extensibility and maintainability by organizing features into self-contained, reusable components that integrate seamlessly with the core JobON application.*
