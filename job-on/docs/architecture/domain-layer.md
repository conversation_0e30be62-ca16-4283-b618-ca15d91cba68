# Domain Layer Documentation

## 🎯 Overview

The Domain Layer contains the core business logic, entities, and rules that define the JobON application. This layer is independent of external concerns and represents the heart of the business domain.

## 📊 Core Domain Models

### **User Model** (`app/Models/User.php`)

The central entity representing all system users with role-based functionality.

```php
class User extends Authenticatable implements HasMedia, MustVerifyEmail, JWTSubject
{
    use HasFactory, HasRoles, InteractsWithMedia, Notifiable, SoftDeletes, Sluggable;
}
```

**Key Properties:**
- `name`, `email`, `phone` - Basic user information
- `type` - User type (individual/company)
- `status` - Account status
- `is_featured` - Featured provider flag
- `experience_interval`, `experience_duration` - Provider experience
- `company_*` - Company-specific fields
- `fcm_token` - Push notification token
- `business_uuid` - External business identifier
- `certificates`, `certificates_status` - Provider certifications

**Relationships:**
- `hasMany(Booking::class)` - User bookings
- `hasMany(Service::class)` - Provider services
- `hasMany(Address::class)` - User addresses
- `belongsToMany(Service::class, 'user_expertise_services')` - Expertise
- `hasOne(BankDetail::class)` - Banking information
- `hasMany(TimeSlot::class)` - Availability slots
- `belongsTo(Company::class)` - Company association

### **JobBooking Model** (`app/Models/JobBooking.php`)

Represents job posting requests from customers.

```php
class JobBooking extends Model
{
    protected $table = 'job_bookings';
    
    protected $casts = [
        'schedule_date' => 'date',
        'service_tasks' => 'array',
    ];
}
```

**Key Properties:**
- `job_uuid` - Unique job identifier
- `project_code` - Human-readable job code
- `job_type` - Type of job (send_bids/find_providers)
- `property_type` - Property classification
- `service_category` - Service type
- `service_tasks` - Array of required tasks
- `schedule_date` - Preferred completion date
- `frequency` - One-time or recurring
- `address`, `city`, `state`, `zip_code` - Location details
- `contact_*` - Customer contact information
- `status` - Current job status
- `user_id` - Customer reference

**Business Rules:**
- Service tasks are stored as JSON array
- Schedule date must be in the future
- Status follows defined workflow states

### **Bid Model** (`app/Models/Bid.php`)

Represents provider bids on job bookings.

```php
class Bid extends Model
{
    protected $casts = [
        'amount' => 'float',
        'estimated_completion_time' => 'datetime',
    ];
}
```

**Key Properties:**
- `job_booking_id` - Associated job booking
- `provider_id` - Bidding provider
- `amount` - Bid amount
- `description` - Bid details
- `status` - Bid status (requested/accepted/rejected)
- `estimated_completion_time` - Completion estimate

**Relationships:**
- `belongsTo(JobBooking::class)` - Associated job
- `belongsTo(User::class, 'provider_id')` - Bidding provider
- `hasOne(Job::class)` - Created job (if accepted)

**Status Methods:**
- `isRequested()` - Check if bid is pending
- `isAccepted()` - Check if bid is accepted
- `isRejected()` - Check if bid is rejected

### **Job Model** (`app/Models/Job.php`)

Represents active jobs created from accepted bids.

```php
class Job extends Model
{
    protected $table = 'business_jobs';
    
    protected $casts = [
        'agreed_amount' => 'decimal:2',
        'estimated_completion_time' => 'datetime',
        'actual_start_time' => 'datetime',
        'actual_completion_time' => 'datetime',
    ];
}
```

**Key Properties:**
- `job_uuid` - Unique job identifier
- `job_booking_id` - Source job booking
- `bid_id` - Accepted bid
- `customer_id`, `provider_id` - Involved parties
- `status` - Current job status
- `agreed_amount` - Final agreed price
- `estimated_completion_time` - Expected completion
- `actual_start_time`, `actual_completion_time` - Actual times
- `notes` - Job notes and updates

### **Service Model** (`app/Models/Service.php`)

Represents services offered by providers.

```php
class Service extends Model implements HasMedia
{
    use HasFactory, InteractsWithMedia, Sluggable, SoftDeletes;
    
    protected $withCount = ['bookings', 'reviews'];
    protected $appends = ['review_ratings', 'rating_count'];
}
```

**Key Properties:**
- `title` - Service name
- `price` - Base service price
- `duration`, `duration_unit` - Service duration
- `description`, `content` - Service details
- `address_id` - Service location
- `user_id` - Provider reference
- `type` - Service type
- `is_featured` - Featured service flag
- `required_servicemen` - Team size requirement
- `service_rate` - Rating score

**Relationships:**
- `belongsTo(User::class)` - Service provider
- `hasMany(Booking::class)` - Service bookings
- `belongsToMany(Category::class)` - Service categories
- `hasMany(Review::class)` - Customer reviews

### **Booking Model** (`app/Models/Booking.php`)

Represents traditional service bookings (separate from job bookings).

**Key Relationships (Eager Loaded):**
```php
protected $with = [
    'provider:id,name,experience_interval,experience_duration,email,phone,fcm_token,code',
    'service.addresses',
    'servicemen:id,name,experience_interval,experience_duration,email,phone,fcm_token',
    'coupon:code,amount,type',
    'booking_status:id,name,slug,hexa_code',
    'booking_status_logs',
    'consumer',
    'address',
    'bookingReasons',
    'serviceProofs',
    'extra_charges',
    'additional_services',
];
```

## 🔄 Domain Events & Workflows

### **Job Booking Workflow**
1. Customer creates `JobBooking`
2. System triggers job notification campaign
3. Providers submit `Bid` entities
4. Customer accepts/rejects bids
5. Accepted bid creates `Job` entity
6. Job progresses through status workflow

### **Notification Campaign Workflow**
1. `JobNotificationCampaign` created for new job
2. System finds eligible providers within radius
3. `JobNotificationRecipient` records created
4. Email notifications sent to providers
5. Campaign tracking and analytics updated

### **Service Booking Workflow**
1. Customer selects `Service`
2. `Booking` entity created
3. Provider assignment
4. Service execution and completion
5. Review and rating collection

## 📋 Business Rules & Enums

### **Role-Based Access (RoleEnum)**
- `ADMIN` - System administration
- `PROVIDER` - Service providers
- `CONSUMER` - Customers
- `SUPREME_ADMIN` - Super administrator

### **Status Enums**
- `JobBookingStatusEnum` - Job booking states
- `BidStatusEnum` - Bid states
- `JobNotificationRecipientStatusEnum` - Notification states

### **Business Validation Rules**
- Users must have verified email for certain actions
- Providers must have complete profiles to bid
- Job bookings require valid location data
- Bids must be within reasonable amount ranges
- Jobs can only be created from accepted bids

## 🔗 Model Relationships Map

```
User (Provider/Consumer/Admin)
├── hasMany(JobBooking) [as customer]
├── hasMany(Bid) [as provider]
├── hasMany(Job) [as provider/customer]
├── hasMany(Service) [as provider]
├── hasMany(Booking) [as provider/consumer]
├── hasMany(Address)
├── hasOne(BankDetail)
├── belongsTo(Company)
└── belongsToMany(Service) [expertise]

JobBooking
├── belongsTo(User) [customer]
├── hasMany(Bid)
├── hasOne(JobNotificationCampaign)
└── hasMany(Job) [through bids]

Bid
├── belongsTo(JobBooking)
├── belongsTo(User) [provider]
└── hasOne(Job) [if accepted]

Job
├── belongsTo(JobBooking)
├── belongsTo(Bid)
├── belongsTo(User) [customer]
└── belongsTo(User) [provider]

Service
├── belongsTo(User) [provider]
├── hasMany(Booking)
├── belongsToMany(Category)
└── hasMany(Review)
```

## 🎯 Domain Services

### **Job Matching Service**
- Finds eligible providers for job bookings
- Calculates distance and availability
- Manages notification campaigns

### **Bid Management Service**
- Validates bid submissions
- Handles bid acceptance/rejection
- Creates jobs from accepted bids

### **Rating & Review Service**
- Manages service ratings
- Calculates provider scores
- Handles review workflows

---

*The Domain Layer forms the core of the JobON application, defining the business entities and rules that drive all system functionality.*
