# Database Layer Documentation

## 🎯 Overview

The Database Layer manages data persistence, schema definitions, and data relationships for the JobON application. It uses MySQL with spatial data support for location-based features.

## 🗄️ Database Schema Overview

### **Core Tables Structure**

```sql
-- Users table (central entity)
users
├── id (primary key)
├── name, email, phone (basic info)
├── type (individual/company)
├── status (active/inactive/suspended)
├── role-related fields
├── provider-specific fields
├── location fields (lat/lng)
├── fcm_token (notifications)
├── timestamps
└── soft_deletes

-- Job bookings table
job_bookings
├── id (primary key)
├── job_uuid (unique identifier)
├── project_code (human readable)
├── user_id (foreign key to users)
├── job_type, property_type
├── service_category, service_tasks (JSON)
├── location fields
├── contact information
├── schedule_date, time_preference
├── status
└── timestamps

-- Bids table
bids
├── id (primary key)
├── job_booking_id (foreign key)
├── provider_id (foreign key to users)
├── amount, description
├── status (requested/accepted/rejected)
├── estimated_completion_time
└── timestamps

-- Jobs table (business_jobs)
business_jobs
├── id (primary key)
├── job_uuid (unique identifier)
├── job_booking_id (foreign key)
├── bid_id (foreign key)
├── customer_id, provider_id (foreign keys to users)
├── status, agreed_amount
├── time tracking fields
└── timestamps
```

## 📊 Key Relationships

### **User Relationships**
```sql
-- One user can have many job bookings (as customer)
users.id → job_bookings.user_id

-- One user can have many bids (as provider)
users.id → bids.provider_id

-- One user can have many jobs (as customer or provider)
users.id → business_jobs.customer_id
users.id → business_jobs.provider_id

-- One user can have many services (as provider)
users.id → services.user_id

-- One user can have many addresses
users.id → addresses.user_id
```

### **Job Flow Relationships**
```sql
-- Job booking to bids (one-to-many)
job_bookings.id → bids.job_booking_id

-- Bid to job (one-to-one when accepted)
bids.id → business_jobs.bid_id

-- Job booking to job (one-to-many through bids)
job_bookings.id → business_jobs.job_booking_id
```

## 🔧 Migration Patterns

### **Safe Migration Structure**
```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('job_bookings', function (Blueprint $table) {
            $table->id();
            $table->uuid('job_uuid')->unique();
            $table->string('project_code')->unique();
            
            // Foreign keys with proper constraints
            $table->foreignId('user_id')
                  ->constrained('users')
                  ->onDelete('cascade');
            
            // Enum-like fields with validation
            $table->enum('job_type', ['send_bids', 'find_providers']);
            $table->enum('property_type', ['residential', 'commercial', 'industrial']);
            
            // JSON fields for flexible data
            $table->json('service_tasks');
            
            // Location fields with spatial indexing
            $table->string('address');
            $table->string('city');
            $table->string('state');
            $table->string('zip_code');
            $table->decimal('latitude', 10, 8)->nullable();
            $table->decimal('longitude', 11, 8)->nullable();
            
            // Status with default
            $table->string('status')->default('pending');
            
            // Timestamps
            $table->timestamps();
            
            // Indexes for performance
            $table->index(['status', 'created_at']);
            $table->index(['service_category', 'zip_code']);
            $table->index(['user_id', 'status']);
            
            // Spatial index for location queries
            $table->spatialIndex(['latitude', 'longitude']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('job_bookings');
    }
};
```

### **Adding Columns Safely**
```php
public function up(): void
{
    Schema::table('job_bookings', function (Blueprint $table) {
        // Check if column doesn't exist
        if (!Schema::hasColumn('job_bookings', 'priority_level')) {
            $table->enum('priority_level', ['low', 'medium', 'high'])
                  ->default('medium')
                  ->after('status');
        }
        
        // Add index if it doesn't exist
        if (!$this->hasIndex('job_bookings', 'job_bookings_priority_level_index')) {
            $table->index('priority_level');
        }
    });
}

private function hasIndex(string $table, string $index): bool
{
    $indexes = Schema::getConnection()
        ->getDoctrineSchemaManager()
        ->listTableIndexes($table);
        
    return array_key_exists($index, $indexes);
}
```

## 🌱 Seeder Patterns

### **Base Seeder Structure**
```php
<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class DatabaseSeeder extends Seeder
{
    public function run(): void
    {
        // Disable foreign key checks for seeding
        DB::statement('SET FOREIGN_KEY_CHECKS=0;');
        
        $this->call([
            RolePermissionSeeder::class,
            CountryStateSeeder::class,
            CategorySeeder::class,
            ServiceSeeder::class,
            UserSeeder::class,
            // Add other seeders in dependency order
        ]);
        
        // Re-enable foreign key checks
        DB::statement('SET FOREIGN_KEY_CHECKS=1;');
    }
}
```

### **Role and Permission Seeder**
```php
class RolePermissionSeeder extends Seeder
{
    public function run(): void
    {
        // Create roles
        $roles = [
            'admin' => 'System Administrator',
            'supreme_admin' => 'Supreme Administrator', 
            'provider' => 'Service Provider',
            'consumer' => 'Service Consumer',
        ];

        foreach ($roles as $name => $description) {
            Role::firstOrCreate(
                ['name' => $name, 'guard_name' => 'api'],
                ['description' => $description]
            );
        }

        // Create permissions
        $permissions = [
            'manage_users',
            'manage_job_bookings',
            'manage_bids',
            'manage_payments',
            'view_analytics',
        ];

        foreach ($permissions as $permission) {
            Permission::firstOrCreate([
                'name' => $permission,
                'guard_name' => 'api'
            ]);
        }

        // Assign permissions to roles
        $adminRole = Role::findByName('admin', 'api');
        $adminRole->givePermissionTo($permissions);
    }
}
```

### **Location Data Seeder**
```php
class CountryStateSeeder extends Seeder
{
    public function run(): void
    {
        $countries = [
            [
                'name' => 'United States',
                'code' => 'US',
                'states' => [
                    ['name' => 'California', 'code' => 'CA'],
                    ['name' => 'New York', 'code' => 'NY'],
                    ['name' => 'Texas', 'code' => 'TX'],
                    // ... more states
                ]
            ],
            // ... more countries
        ];

        foreach ($countries as $countryData) {
            $country = Country::firstOrCreate([
                'name' => $countryData['name'],
                'code' => $countryData['code']
            ]);

            foreach ($countryData['states'] as $stateData) {
                State::firstOrCreate([
                    'country_id' => $country->id,
                    'name' => $stateData['name'],
                    'code' => $stateData['code']
                ]);
            }
        }
    }
}
```

## 🏭 Factory Patterns

### **User Factory**
```php
<?php

namespace Database\Factories;

use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

class UserFactory extends Factory
{
    protected $model = User::class;

    public function definition(): array
    {
        return [
            'name' => $this->faker->name(),
            'email' => $this->faker->unique()->safeEmail(),
            'phone' => $this->faker->phoneNumber(),
            'email_verified_at' => now(),
            'password' => Hash::make('password'),
            'remember_token' => Str::random(10),
            'type' => $this->faker->randomElement(['individual', 'company']),
            'status' => 'active',
            'is_featured' => $this->faker->boolean(20), // 20% chance
            'latitude' => $this->faker->latitude(25, 50), // US bounds
            'longitude' => $this->faker->longitude(-125, -65),
        ];
    }

    public function provider(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'company_name' => $this->faker->company(),
                'experience_duration' => $this->faker->numberBetween(1, 20),
                'experience_interval' => $this->faker->randomElement(['months', 'years']),
                'description' => $this->faker->paragraph(),
            ];
        });
    }

    public function consumer(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'company_name' => null,
                'experience_duration' => null,
                'experience_interval' => null,
            ];
        });
    }

    public function verified(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'email_verified_at' => now(),
                'is_verified' => true,
            ];
        });
    }
}
```

### **JobBooking Factory**
```php
class JobBookingFactory extends Factory
{
    protected $model = JobBooking::class;

    public function definition(): array
    {
        return [
            'job_uuid' => $this->faker->uuid(),
            'project_code' => 'JOB-' . date('Y') . '-' . $this->faker->unique()->numberBetween(1000, 9999),
            'job_type' => $this->faker->randomElement(['send_bids', 'find_providers']),
            'property_type' => $this->faker->randomElement(['residential', 'commercial', 'industrial']),
            'service_category' => $this->faker->randomElement(['cleaning', 'plumbing', 'electrical', 'painting']),
            'service_tasks' => $this->faker->randomElements([
                'deep_cleaning', 'window_cleaning', 'carpet_cleaning',
                'pipe_repair', 'drain_cleaning', 'fixture_installation',
                'wiring', 'outlet_installation', 'lighting',
                'interior_painting', 'exterior_painting', 'wall_preparation'
            ], $this->faker->numberBetween(1, 3)),
            'description' => $this->faker->paragraph(),
            'schedule_date' => $this->faker->dateTimeBetween('+1 day', '+30 days'),
            'time_preference' => $this->faker->randomElement(['morning', 'afternoon', 'evening', 'flexible']),
            'frequency' => $this->faker->randomElement(['one-time', 'weekly', 'monthly']),
            'address' => $this->faker->streetAddress(),
            'city' => $this->faker->city(),
            'state' => $this->faker->stateAbbr(),
            'zip_code' => $this->faker->postcode(),
            'contact_name' => $this->faker->name(),
            'contact_email' => $this->faker->email(),
            'contact_phone' => $this->faker->phoneNumber(),
            'status' => $this->faker->randomElement(['pending', 'open', 'assigned', 'completed']),
        ];
    }

    public function open(): static
    {
        return $this->state(['status' => 'open']);
    }

    public function withUser(User $user): static
    {
        return $this->state(['user_id' => $user->id]);
    }
}
```

## 📈 Performance Optimization

### **Database Indexing Strategy**
```sql
-- Composite indexes for common queries
CREATE INDEX idx_job_bookings_status_created ON job_bookings(status, created_at);
CREATE INDEX idx_job_bookings_category_location ON job_bookings(service_category, zip_code);
CREATE INDEX idx_bids_job_provider ON bids(job_booking_id, provider_id);
CREATE INDEX idx_users_role_status ON users(role, status);

-- Spatial indexes for location queries
CREATE SPATIAL INDEX idx_users_location ON users(latitude, longitude);
CREATE SPATIAL INDEX idx_job_bookings_location ON job_bookings(latitude, longitude);

-- Full-text indexes for search
CREATE FULLTEXT INDEX idx_job_bookings_search ON job_bookings(description, service_category);
CREATE FULLTEXT INDEX idx_services_search ON services(title, description, content);
```

### **Query Optimization Patterns**
```php
// Efficient location-based queries
$nearbyProviders = User::role('provider')
    ->selectRaw('*, ST_Distance_Sphere(POINT(longitude, latitude), POINT(?, ?)) as distance', [$lng, $lat])
    ->whereRaw('ST_Distance_Sphere(POINT(longitude, latitude), POINT(?, ?)) <= ?', [$lng, $lat, $radiusMeters])
    ->orderBy('distance')
    ->limit(50)
    ->get();

// Efficient eager loading
$jobBookings = JobBooking::with([
    'user:id,name,email',
    'bids' => function ($query) {
        $query->with('provider:id,name,rating')
              ->where('status', 'requested')
              ->orderBy('amount');
    }
])->paginate(15);
```

## 🔄 Database Maintenance

### **Regular Maintenance Tasks**
```php
// Clean up expired tokens
DB::table('personal_access_tokens')
    ->where('expires_at', '<', now())
    ->delete();

// Archive old completed jobs
DB::table('business_jobs')
    ->where('status', 'completed')
    ->where('updated_at', '<', now()->subMonths(6))
    ->update(['archived' => true]);

// Update user statistics
DB::statement('
    UPDATE users 
    SET total_jobs = (
        SELECT COUNT(*) 
        FROM business_jobs 
        WHERE provider_id = users.id AND status = "completed"
    )
    WHERE role = "provider"
');
```

### **Backup Strategy**
```bash
# Daily backup script
mysqldump --single-transaction --routines --triggers jobon_db > backup_$(date +%Y%m%d).sql

# Backup with compression
mysqldump --single-transaction jobon_db | gzip > backup_$(date +%Y%m%d).sql.gz
```

---

*The Database Layer provides the foundation for all data operations in JobON, ensuring data integrity, performance, and scalability through proper schema design and optimization strategies.*
