# Application Layer Documentation

## 🎯 Overview

The Application Layer serves as the interface between the presentation layer and the domain layer. It handles HTTP requests, coordinates business operations, and manages cross-cutting concerns like authentication, validation, and response formatting.

## 🏗️ Layer Components

### **Controllers** (`app/Http/Controllers/`)

Controllers handle HTTP requests and coordinate application flow without containing business logic.

#### **API Controllers** (`app/Http/Controllers/API/`)

**JobBookingController**
```php
class JobBookingController extends Controller
{
    public function store(JobBookingRequest $request)
    {
        $jobBooking = $this->jobBookingService->create($request->validated());
        
        return new JobBookingResource($jobBooking);
    }
    
    public function index(Request $request)
    {
        $jobBookings = $this->jobBookingService->getFilteredJobBookings(
            $request->only(['status', 'service_category', 'zip_code'])
        );
        
        return JobBookingResource::collection($jobBookings);
    }
}
```

**Key Responsibilities**:
- Request delegation to services
- Response formatting via resources
- HTTP status code management
- Basic request validation coordination

**BidController**
```php
class BidController extends Controller
{
    public function store(BidRequest $request)
    {
        $bid = $this->bidService->submitBid(
            $request->validated(),
            auth()->user()
        );
        
        return new BidResource($bid);
    }
    
    public function updateStatus(Request $request, Bid $bid)
    {
        $this->authorize('update', $bid);
        
        $updatedBid = $this->bidService->updateStatus(
            $bid,
            $request->status
        );
        
        return new BidResource($updatedBid);
    }
}
```

#### **Backend Controllers** (`app/Http/Controllers/Backend/`)

Handle admin panel functionality with proper authorization.

**AdminJobBookingController**
```php
class AdminJobBookingController extends Controller
{
    public function index()
    {
        $this->authorize('viewAny', JobBooking::class);
        
        $jobBookings = JobBooking::with(['user', 'bids'])
            ->paginate(15);
            
        return view('backend.job-bookings.index', compact('jobBookings'));
    }
}
```

#### **Frontend Controllers** (`app/Http/Controllers/Frontend/`)

Handle web interface requests with session-based authentication.

### **Middleware** (`app/Http/Middleware/`)

Cross-cutting concerns that process requests before reaching controllers.

#### **Authentication Middleware**
```php
class Authenticate extends Middleware
{
    protected function redirectTo($request)
    {
        if (!$request->expectsJson()) {
            return route('frontend.login');
        }
    }
}
```

#### **Authorization Middleware**
```php
class AuthorizeCanAny extends Middleware
{
    public function handle($request, Closure $next, ...$abilities)
    {
        foreach ($abilities as $ability) {
            if (Gate::allows($ability)) {
                return $next($request);
            }
        }
        
        abort(403);
    }
}
```

#### **Localization Middleware**
```php
class Localization extends Middleware
{
    public function handle($request, Closure $next)
    {
        $locale = $request->header('Accept-Language', 'en');
        App::setLocale($locale);
        
        return $next($request);
    }
}
```

### **Request Classes** (`app/Http/Requests/`)

Handle input validation and authorization at the request level.

#### **API Requests** (`app/Http/Requests/API/`)

**JobBookingRequest**
```php
class JobBookingRequest extends FormRequest
{
    public function authorize()
    {
        return auth()->check() && auth()->user()->hasRole('consumer');
    }
    
    public function rules()
    {
        return [
            'job_type' => 'required|in:send_bids,find_providers',
            'property_type' => 'required|in:residential,commercial,industrial',
            'service_category' => 'required|string|max:100',
            'service_tasks' => 'required|array|min:1',
            'service_tasks.*' => 'string|max:100',
            'description' => 'required|string|max:1000',
            'schedule_date' => 'required|date|after:today',
            'address' => 'required|string|max:255',
            'city' => 'required|string|max:100',
            'state' => 'required|string|max:100',
            'zip_code' => 'required|string|max:10',
            'contact_name' => 'required|string|max:100',
            'contact_email' => 'required|email|max:255',
            'contact_phone' => 'required|string|max:20',
        ];
    }
    
    public function messages()
    {
        return [
            'schedule_date.after' => 'Schedule date must be at least 24 hours in the future.',
            'service_tasks.min' => 'At least one service task is required.',
        ];
    }
}
```

**BidRequest**
```php
class BidRequest extends FormRequest
{
    public function authorize()
    {
        return auth()->check() && 
               auth()->user()->hasRole('provider') &&
               auth()->user()->is_verified;
    }
    
    public function rules()
    {
        return [
            'job_booking_id' => [
                'required',
                'exists:job_bookings,id',
                Rule::unique('bids')->where(function ($query) {
                    return $query->where('provider_id', auth()->id());
                }),
            ],
            'amount' => 'required|numeric|min:1|max:10000',
            'description' => 'required|string|max:1000',
            'estimated_completion_time' => 'required|date|after:now',
        ];
    }
}
```

#### **Backend Requests** (`app/Http/Requests/Backend/`)

Handle admin panel form validation with additional authorization checks.

### **Resources** (`app/Http/Resources/`)

Transform models into consistent API responses.

#### **JobBookingResource**
```php
class JobBookingResource extends JsonResource
{
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'job_uuid' => $this->job_uuid,
            'project_code' => $this->project_code,
            'job_type' => $this->job_type,
            'property_type' => $this->property_type,
            'service_category' => $this->service_category,
            'service_tasks' => $this->service_tasks,
            'description' => $this->description,
            'schedule_date' => $this->schedule_date->format('Y-m-d'),
            'time_preference' => $this->time_preference,
            'frequency' => $this->frequency,
            'status' => $this->status,
            'location' => [
                'address' => $this->address,
                'city' => $this->city,
                'state' => $this->state,
                'zip_code' => $this->zip_code,
            ],
            'contact' => [
                'name' => $this->contact_name,
                'email' => $this->contact_email,
                'phone' => $this->contact_phone,
            ],
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            
            // Conditional includes
            'bids' => BidResource::collection($this->whenLoaded('bids')),
            'bids_count' => $this->when(isset($this->bids_count), $this->bids_count),
            'customer' => new UserResource($this->whenLoaded('user')),
        ];
    }
}
```

#### **BidResource**
```php
class BidResource extends JsonResource
{
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'job_booking_id' => $this->job_booking_id,
            'amount' => number_format($this->amount, 2),
            'description' => $this->description,
            'status' => $this->status,
            'estimated_completion_time' => $this->estimated_completion_time,
            'created_at' => $this->created_at,
            
            // Provider information
            'provider' => new UserResource($this->whenLoaded('provider')),
            
            // Job booking information (when needed)
            'job_booking' => new JobBookingResource($this->whenLoaded('jobBooking')),
        ];
    }
}
```

#### **UserResource**
```php
class UserResource extends JsonResource
{
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'email' => $this->when($this->shouldShowEmail($request), $this->email),
            'phone' => $this->when($this->shouldShowPhone($request), $this->phone),
            'role' => $this->role,
            'status' => $this->status,
            'is_verified' => $this->is_verified,
            'is_featured' => $this->is_featured,
            
            // Provider-specific fields
            'company_name' => $this->when($this->hasRole('provider'), $this->company_name),
            'experience_duration' => $this->when($this->hasRole('provider'), $this->experience_duration),
            'experience_interval' => $this->when($this->hasRole('provider'), $this->experience_interval),
            'description' => $this->when($this->hasRole('provider'), $this->description),
            
            // Ratings and reviews
            'rating' => $this->when(isset($this->review_ratings), $this->review_ratings),
            'reviews_count' => $this->when(isset($this->reviews_count), $this->reviews_count),
            
            // Media
            'avatar' => $this->when($this->hasMedia('avatar'), $this->getFirstMediaUrl('avatar')),
            
            'created_at' => $this->created_at,
        ];
    }
    
    private function shouldShowEmail($request)
    {
        return $request->user() && 
               ($request->user()->id === $this->id || 
                $request->user()->hasRole(['admin', 'supreme_admin']));
    }
}
```

## 🔒 Authorization Patterns

### **Policy-Based Authorization**
```php
// In Controller
$this->authorize('view', $jobBooking);
$this->authorize('update', $bid);

// In Request
public function authorize()
{
    return $this->user()->can('create', JobBooking::class);
}
```

### **Role-Based Middleware**
```php
// In routes
Route::middleware(['auth:api', 'role:provider'])->group(function () {
    Route::post('/bids', [BidController::class, 'store']);
});
```

## 🎯 Response Patterns

### **Success Responses**
```php
// Single resource
return new JobBookingResource($jobBooking);

// Collection
return JobBookingResource::collection($jobBookings);

// With custom message
return response()->json([
    'success' => true,
    'message' => 'Job booking created successfully',
    'data' => new JobBookingResource($jobBooking)
], 201);
```

### **Error Responses**
```php
// Validation errors (handled by FormRequest)
// Returns 422 with validation messages

// Authorization errors
abort(403, 'You are not authorized to perform this action');

// Not found errors
abort(404, 'Job booking not found');

// Custom business logic errors
return response()->json([
    'success' => false,
    'message' => 'Cannot bid on your own job posting',
    'code' => 'INVALID_BID_ATTEMPT'
], 400);
```

## 🔄 Request Flow

```
HTTP Request
    ↓
Middleware Stack
    ↓
Route Resolution
    ↓
Controller Method
    ↓
Request Validation (FormRequest)
    ↓
Authorization Check (Policy)
    ↓
Service Layer Call
    ↓
Resource Transformation
    ↓
HTTP Response
```

## 📊 Performance Considerations

### **Eager Loading**
```php
// In controllers, load relationships efficiently
$jobBookings = JobBooking::with(['user', 'bids.provider'])
    ->paginate(15);
```

### **Resource Optimization**
```php
// Conditional loading in resources
'bids' => BidResource::collection($this->whenLoaded('bids')),
'expensive_calculation' => $this->when($request->include_stats, $this->calculateStats()),
```

### **Caching**
```php
// Cache expensive operations
$stats = Cache::remember("user_stats_{$user->id}", 3600, function () use ($user) {
    return $this->calculateUserStats($user);
});
```

---

*The Application Layer ensures clean separation between HTTP concerns and business logic while providing consistent interfaces for both web and API clients.*
