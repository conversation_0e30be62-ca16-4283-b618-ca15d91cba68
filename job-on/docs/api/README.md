# JobON API Documentation

## 🌐 API Overview

The JobON API provides comprehensive access to the job booking platform functionality through RESTful endpoints. The API supports both web and mobile applications with JWT-based authentication.

## 🔐 Authentication

### JWT Authentication
The API uses JSON Web Tokens (JWT) for authentication. Include the token in the Authorization header:

```http
Authorization: Bearer {your-jwt-token}
```

### Authentication Endpoints
```http
POST /api/login                 # User login
POST /api/register              # User registration  
POST /api/provider-register     # Provider registration
POST /api/logout                # User logout
POST /api/refresh               # Token refresh
POST /api/forgot-password       # Password reset request
POST /api/reset-password        # Password reset confirmation
```

## 📊 API Structure

### Base URL
```
Production: https://your-domain.com/api
Development: http://localhost:8000/api
```

### Response Format
All API responses follow a consistent JSON structure:

```json
{
    "success": true,
    "message": "Operation successful",
    "data": {
        // Response data
    },
    "meta": {
        // Pagination or additional metadata
    }
}
```

### Error Response Format
```json
{
    "success": false,
    "message": "Error description",
    "errors": {
        "field": ["Validation error message"]
    },
    "code": 422
}
```

## 🎯 Core API Endpoints

### **Job Booking Endpoints**

#### Create Job Booking
```http
POST /api/job-bookings
Content-Type: application/json
Authorization: Bearer {token}

{
    "job_type": "send_bids",
    "property_type": "residential",
    "service_category": "cleaning",
    "service_tasks": ["deep_cleaning", "window_cleaning"],
    "description": "Need thorough house cleaning",
    "schedule_date": "2024-02-15",
    "time_preference": "morning",
    "frequency": "one-time",
    "address": "123 Main St",
    "city": "New York",
    "state": "NY",
    "zip_code": "10001",
    "contact_name": "John Doe",
    "contact_email": "<EMAIL>",
    "contact_phone": "555-0123"
}
```

#### Get Job Bookings
```http
GET /api/job-bookings
Authorization: Bearer {token}

# Query Parameters:
# ?status=open&page=1&per_page=10
# ?service_category=cleaning
# ?zip_code=10001
```

#### Get Job Booking Details
```http
GET /api/job-bookings/{id}
Authorization: Bearer {token}
```

### **Bid Management Endpoints**

#### Submit Bid
```http
POST /api/bids
Content-Type: application/json
Authorization: Bearer {token}

{
    "job_booking_id": 123,
    "amount": 150.00,
    "description": "I can complete this job with high quality",
    "estimated_completion_time": "2024-02-15 14:00:00"
}
```

#### Get Provider Bids
```http
GET /api/provider/bids
Authorization: Bearer {token}

# Query Parameters:
# ?status=requested&page=1
# ?job_booking_id=123
```

#### Accept/Reject Bid
```http
PATCH /api/bids/{id}/status
Content-Type: application/json
Authorization: Bearer {token}

{
    "status": "accepted"  // or "rejected"
}
```

### **Job Management Endpoints**

#### Get Active Jobs
```http
GET /api/jobs
Authorization: Bearer {token}

# Query Parameters:
# ?status=in_progress
# ?provider_id=456
# ?customer_id=789
```

#### Update Job Status
```http
PATCH /api/jobs/{id}/status
Content-Type: application/json
Authorization: Bearer {token}

{
    "status": "completed",
    "notes": "Job completed successfully"
}
```

### **User Management Endpoints**

#### Get User Profile
```http
GET /api/profile
Authorization: Bearer {token}
```

#### Update Profile
```http
PUT /api/profile
Content-Type: application/json
Authorization: Bearer {token}

{
    "name": "John Doe",
    "email": "<EMAIL>",
    "phone": "555-0123",
    "description": "Experienced cleaning professional"
}
```

#### Get Provider Statistics
```http
GET /api/statistics/count
Authorization: Bearer {token}
```

### **Service Endpoints**

#### Get Services
```http
GET /api/service
# Query Parameters:
# ?category_id=1&featured=1
# ?search=cleaning&location=10001
```

#### Get Service Details
```http
GET /api/service/{id}
```

#### Get Service Packages
```http
GET /api/servicePackages
GET /api/servicePackages/{service_id}
```

### **Location & Zone Endpoints**

#### Get Zones by Point
```http
GET /api/zone-by-point?lat=40.7128&lng=-74.0060
```

#### Get States
```http
GET /api/state
```

#### Get Countries
```http
GET /api/country
```

## 📱 Mobile App Specific Endpoints

### **Provider App Endpoints**
```http
GET /api/provider/dashboard        # Provider dashboard data
GET /api/provider/jobs            # Provider job list
GET /api/provider/earnings        # Earnings summary
POST /api/provider/availability   # Update availability
```

### **Consumer App Endpoints**
```http
GET /api/consumer/bookings        # Consumer booking history
GET /api/consumer/favorites       # Favorite providers
POST /api/consumer/review         # Submit review
```

## 🔄 Pagination

API endpoints that return lists support pagination:

```json
{
    "data": [...],
    "meta": {
        "current_page": 1,
        "from": 1,
        "last_page": 5,
        "per_page": 15,
        "to": 15,
        "total": 75
    },
    "links": {
        "first": "http://api.example.com/endpoint?page=1",
        "last": "http://api.example.com/endpoint?page=5",
        "prev": null,
        "next": "http://api.example.com/endpoint?page=2"
    }
}
```

## 🎨 API Resources

### **JobBookingResource**
```json
{
    "id": 123,
    "job_uuid": "job_abc123",
    "project_code": "JOB-2024-001",
    "job_type": "send_bids",
    "property_type": "residential",
    "service_category": "cleaning",
    "service_tasks": ["deep_cleaning", "window_cleaning"],
    "description": "Need thorough house cleaning",
    "schedule_date": "2024-02-15",
    "status": "open",
    "address": "123 Main St",
    "city": "New York",
    "state": "NY",
    "zip_code": "10001",
    "contact_name": "John Doe",
    "contact_email": "<EMAIL>",
    "contact_phone": "555-0123",
    "created_at": "2024-01-15T10:00:00Z",
    "bids_count": 5
}
```

### **BidResource**
```json
{
    "id": 456,
    "job_booking_id": 123,
    "provider": {
        "id": 789,
        "name": "ABC Cleaning Services",
        "rating": 4.8,
        "reviews_count": 150
    },
    "amount": 150.00,
    "description": "I can complete this job with high quality",
    "status": "requested",
    "estimated_completion_time": "2024-02-15T14:00:00Z",
    "created_at": "2024-01-15T11:00:00Z"
}
```

### **UserResource**
```json
{
    "id": 789,
    "name": "John Doe",
    "email": "<EMAIL>",
    "phone": "555-0123",
    "role": "provider",
    "status": "active",
    "is_verified": true,
    "rating": 4.8,
    "reviews_count": 150,
    "experience_duration": 5,
    "experience_interval": "years",
    "company_name": "ABC Cleaning Services",
    "description": "Professional cleaning service",
    "created_at": "2023-01-01T00:00:00Z"
}
```

## 🚨 Error Codes

| Code | Description |
|------|-------------|
| 200  | Success |
| 201  | Created |
| 400  | Bad Request |
| 401  | Unauthorized |
| 403  | Forbidden |
| 404  | Not Found |
| 422  | Validation Error |
| 429  | Rate Limit Exceeded |
| 500  | Internal Server Error |

## 🔒 Rate Limiting

API endpoints are rate limited to prevent abuse:
- **Authenticated requests**: 60 requests per minute
- **Unauthenticated requests**: 30 requests per minute
- **Password reset requests**: 5 requests per hour

Rate limit headers are included in responses:
```http
X-RateLimit-Limit: 60
X-RateLimit-Remaining: 59
X-RateLimit-Reset: **********
```

---

*For detailed endpoint documentation and request/response examples, refer to the individual endpoint documentation files.*
