# API Endpoints Documentation

## 🔐 Authentication Endpoints

### **User Authentication**

```http
POST /api/login
Content-Type: application/json

{
    "email": "<EMAIL>",
    "password": "password"
}

Response:
{
    "success": true,
    "message": "Login successful",
    "data": {
        "user": { /* UserResource */ },
        "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
        "token_type": "bearer",
        "expires_in": 3600
    }
}
```

```http
POST /api/register
Content-Type: application/json

{
    "name": "<PERSON>",
    "email": "<EMAIL>",
    "password": "password",
    "password_confirmation": "password",
    "phone": "555-0123",
    "type": "individual"
}
```

```http
POST /api/provider-register
Content-Type: application/json

{
    "name": "ABC Services",
    "email": "<EMAIL>",
    "password": "password",
    "password_confirmation": "password",
    "phone": "555-0123",
    "company_name": "ABC Cleaning Services",
    "experience_duration": 5,
    "experience_interval": "years",
    "description": "Professional cleaning services"
}
```

```http
POST /api/logout
Authorization: Bearer {token}

POST /api/refresh
Authorization: Bearer {token}

POST /api/forgot-password
Content-Type: application/json

{
    "email": "<EMAIL>"
}

POST /api/reset-password
Content-Type: application/json

{
    "email": "<EMAIL>",
    "token": "reset_token",
    "password": "new_password",
    "password_confirmation": "new_password"
}
```

## 🏠 Job Booking Endpoints

### **Create Job Booking**

```http
POST /api/job-bookings
Authorization: Bearer {token}
Content-Type: application/json

{
    "job_type": "send_bids",
    "property_type": "residential",
    "service_category": "cleaning",
    "service_tasks": ["deep_cleaning", "window_cleaning"],
    "description": "Need thorough house cleaning",
    "schedule_date": "2024-02-15",
    "time_preference": "morning",
    "frequency": "one-time",
    "address": "123 Main St",
    "city": "New York",
    "state": "NY",
    "zip_code": "10001",
    "contact_name": "John Doe",
    "contact_email": "<EMAIL>",
    "contact_phone": "555-0123"
}

Response:
{
    "success": true,
    "message": "Job booking created successfully",
    "data": { /* JobBookingResource */ }
}
```

### **Get Job Bookings**

```http
GET /api/job-bookings
Authorization: Bearer {token}

Query Parameters:
- status: open,assigned,completed
- service_category: cleaning,plumbing,electrical
- zip_code: 10001
- page: 1
- per_page: 15

Response:
{
    "data": [ /* Array of JobBookingResource */ ],
    "links": { /* Pagination links */ },
    "meta": { /* Pagination meta */ }
}
```

### **Get Job Booking Details**

```http
GET /api/job-bookings/{id}
Authorization: Bearer {token}

Response:
{
    "success": true,
    "data": {
        "id": 123,
        "job_uuid": "job_abc123",
        "project_code": "JOB-2024-001",
        "job_type": "send_bids",
        "property_type": "residential",
        "service_category": "cleaning",
        "service_tasks": ["deep_cleaning", "window_cleaning"],
        "description": "Need thorough house cleaning",
        "schedule_date": "2024-02-15",
        "status": "open",
        "location": {
            "address": "123 Main St",
            "city": "New York",
            "state": "NY",
            "zip_code": "10001"
        },
        "contact": {
            "name": "John Doe",
            "email": "<EMAIL>",
            "phone": "555-0123"
        },
        "bids": [ /* Array of BidResource */ ],
        "bids_count": 5,
        "created_at": "2024-01-15T10:00:00Z"
    }
}
```

## 💰 Bid Management Endpoints

### **Submit Bid**

```http
POST /api/bids
Authorization: Bearer {token}
Content-Type: application/json

{
    "job_booking_id": 123,
    "amount": 150.00,
    "description": "I can complete this job with high quality",
    "estimated_completion_time": "2024-02-15 14:00:00"
}

Response:
{
    "success": true,
    "message": "Bid submitted successfully",
    "data": { /* BidResource */ }
}
```

### **Get Provider Bids**

```http
GET /api/provider/bids
Authorization: Bearer {token}

Query Parameters:
- status: requested,accepted,rejected
- job_booking_id: 123
- page: 1

Response:
{
    "data": [ /* Array of BidResource */ ],
    "meta": { /* Pagination meta */ }
}
```

### **Accept/Reject Bid**

```http
PATCH /api/bids/{id}/status
Authorization: Bearer {token}
Content-Type: application/json

{
    "status": "accepted"  // or "rejected"
}

Response:
{
    "success": true,
    "message": "Bid accepted successfully",
    "data": { /* BidResource */ }
}
```

## 🔨 Job Management Endpoints

### **Get Jobs**

```http
GET /api/jobs
Authorization: Bearer {token}

Query Parameters:
- status: assigned,in_progress,completed
- provider_id: 456
- customer_id: 789
- page: 1

Response:
{
    "data": [ /* Array of JobResource */ ],
    "meta": { /* Pagination meta */ }
}
```

### **Update Job Status**

```http
PATCH /api/jobs/{id}/status
Authorization: Bearer {token}
Content-Type: application/json

{
    "status": "in_progress",
    "notes": "Started working on the job"
}

Response:
{
    "success": true,
    "message": "Job status updated successfully",
    "data": { /* JobResource */ }
}
```

## 👤 User Management Endpoints

### **Get User Profile**

```http
GET /api/profile
Authorization: Bearer {token}

Response:
{
    "success": true,
    "data": { /* UserResource */ }
}
```

### **Update Profile**

```http
PUT /api/profile
Authorization: Bearer {token}
Content-Type: application/json

{
    "name": "John Doe",
    "email": "<EMAIL>",
    "phone": "555-0123",
    "description": "Experienced cleaning professional",
    "company_name": "ABC Cleaning"
}

Response:
{
    "success": true,
    "message": "Profile updated successfully",
    "data": { /* UserResource */ }
}
```

### **Upload Profile Image**

```http
POST /api/profile/avatar
Authorization: Bearer {token}
Content-Type: multipart/form-data

avatar: [image file]

Response:
{
    "success": true,
    "message": "Avatar uploaded successfully",
    "data": {
        "avatar_url": "https://example.com/storage/avatars/user_123.jpg"
    }
}
```

### **Get Provider Statistics**

```http
GET /api/statistics/count
Authorization: Bearer {token}

Response:
{
    "success": true,
    "data": {
        "total_jobs": 25,
        "completed_jobs": 20,
        "pending_jobs": 3,
        "in_progress_jobs": 2,
        "total_earnings": 5000.00,
        "average_rating": 4.8,
        "total_reviews": 18
    }
}
```

## 🏪 Service Endpoints

### **Get Services**

```http
GET /api/service

Query Parameters:
- category_id: 1
- featured: 1
- search: cleaning
- location: 10001
- page: 1
- per_page: 15

Response:
{
    "data": [ /* Array of ServiceResource */ ],
    "meta": { /* Pagination meta */ }
}
```

### **Get Service Details**

```http
GET /api/service/{id}

Response:
{
    "success": true,
    "data": {
        "id": 123,
        "title": "Professional House Cleaning",
        "price": 100.00,
        "duration": 2,
        "duration_unit": "hours",
        "description": "Complete house cleaning service",
        "provider": { /* UserResource */ },
        "rating": 4.8,
        "reviews_count": 25,
        "images": [ /* Array of image URLs */ ],
        "categories": [ /* Array of categories */ ]
    }
}
```

### **Get Service Packages**

```http
GET /api/servicePackages
GET /api/servicePackages/{service_id}

Response:
{
    "data": [ /* Array of service packages */ ]
}
```

## 📍 Location Endpoints

### **Get Zones by Point**

```http
GET /api/zone-by-point?lat=40.7128&lng=-74.0060

Response:
{
    "success": true,
    "data": {
        "zone_id": 123,
        "zone_name": "Manhattan",
        "city": "New York",
        "state": "NY",
        "country": "US"
    }
}
```

### **Get States**

```http
GET /api/state

Response:
{
    "data": [
        {
            "id": 1,
            "name": "California",
            "code": "CA",
            "country_id": 1
        }
    ]
}
```

### **Get Countries**

```http
GET /api/country

Response:
{
    "data": [
        {
            "id": 1,
            "name": "United States",
            "code": "US"
        }
    ]
}
```

## 📱 Mobile App Specific Endpoints

### **Provider Dashboard**

```http
GET /api/provider/dashboard
Authorization: Bearer {token}

Response:
{
    "success": true,
    "data": {
        "stats": {
            "pending_jobs": 3,
            "active_jobs": 2,
            "completed_today": 1,
            "earnings_this_month": 1500.00
        },
        "recent_jobs": [ /* Array of recent jobs */ ],
        "pending_bids": [ /* Array of pending bids */ ],
        "notifications": [ /* Array of notifications */ ]
    }
}
```

### **Consumer Bookings**

```http
GET /api/consumer/bookings
Authorization: Bearer {token}

Query Parameters:
- status: pending,active,completed
- page: 1

Response:
{
    "data": [ /* Array of booking history */ ],
    "meta": { /* Pagination meta */ }
}
```

### **Submit Review**

```http
POST /api/consumer/review
Authorization: Bearer {token}
Content-Type: application/json

{
    "job_id": 123,
    "rating": 5,
    "comment": "Excellent service, very professional"
}

Response:
{
    "success": true,
    "message": "Review submitted successfully",
    "data": { /* ReviewResource */ }
}
```

## 🔔 Notification Endpoints

### **Get Notifications**

```http
GET /api/notifications
Authorization: Bearer {token}

Query Parameters:
- unread: true
- page: 1

Response:
{
    "data": [ /* Array of notifications */ ],
    "meta": { /* Pagination meta */ }
}
```

### **Mark Notification as Read**

```http
PATCH /api/notifications/{id}/read
Authorization: Bearer {token}

Response:
{
    "success": true,
    "message": "Notification marked as read"
}
```

### **Update FCM Token**

```http
POST /api/fcm-token
Authorization: Bearer {token}
Content-Type: application/json

{
    "fcm_token": "firebase_token_here"
}

Response:
{
    "success": true,
    "message": "FCM token updated successfully"
}
```

## 🤖 Business Crawler Job Endpoints

### **Get Business Crawler Jobs**

```http
GET /api/business-crawler-jobs
Authorization: Bearer {token} (Admin only)

Query Parameters:
- status: pending,running,paused,completed,failed,cancelled
- campaign_id: 123
- per_page: 15
- page: 1
- sort_by: created_at,updated_at,next_run_at,attempts
- sort_order: asc,desc

Response:
{
    "success": true,
    "data": [ /* Array of BusinessCrawlerJobResource */ ],
    "meta": {
        "pagination": { /* Pagination info */ },
        "statistics": { /* Overall crawler stats */ }
    }
}
```

### **Get Crawler Job Details**

```http
GET /api/business-crawler-jobs/{id}
Authorization: Bearer {token} (Admin only)

Response:
{
    "success": true,
    "data": {
        "id": 123,
        "campaign_id": 456,
        "status": {
            "value": "running",
            "label": "Running",
            "color": "primary"
        },
        "attempts": 3,
        "max_attempts": 10,
        "progress": 30.0,
        "search_criteria": {
            "zip_code": "10001",
            "category": "cleaning",
            "radius": 45.0
        },
        "radius": {
            "original": 30.0,
            "current": 45.0,
            "max": 90.0
        },
        "timing": {
            "created_at": "2024-01-15T10:00:00Z",
            "next_run_at": "2024-01-15T11:30:00Z",
            "estimated_next_run": "in 30 minutes"
        },
        "results": {
            "businesses_found": 0,
            "total_searches_performed": 3
        },
        "actions": {
            "can_pause": true,
            "can_resume": false,
            "can_cancel": true,
            "can_retry": false
        }
    }
}
```

### **Pause Crawler Job**

```http
POST /api/business-crawler-jobs/{id}/pause
Authorization: Bearer {token} (Admin only)
Content-Type: application/json

{
    "reason": "Maintenance window"
}

Response:
{
    "success": true,
    "message": "Crawler job paused successfully",
    "data": { /* Updated BusinessCrawlerJobResource */ }
}
```

### **Resume Crawler Job**

```http
POST /api/business-crawler-jobs/{id}/resume
Authorization: Bearer {token} (Admin only)

Response:
{
    "success": true,
    "message": "Crawler job resumed successfully",
    "data": { /* Updated BusinessCrawlerJobResource */ }
}
```

### **Cancel Crawler Job**

```http
POST /api/business-crawler-jobs/{id}/cancel
Authorization: Bearer {token} (Admin only)
Content-Type: application/json

{
    "reason": "No longer needed"
}

Response:
{
    "success": true,
    "message": "Crawler job cancelled successfully",
    "data": { /* Updated BusinessCrawlerJobResource */ }
}
```

### **Retry Crawler Job**

```http
POST /api/business-crawler-jobs/{id}/retry
Authorization: Bearer {token} (Admin only)

Response:
{
    "success": true,
    "message": "Crawler job retried successfully",
    "data": { /* Updated BusinessCrawlerJobResource */ }
}
```

### **Get Crawler Statistics**

```http
GET /api/business-crawler-jobs/statistics
Authorization: Bearer {token} (Admin only)

Response:
{
    "success": true,
    "data": {
        "overview": {
            "total_jobs": 150,
            "completed_jobs": 120,
            "failed_jobs": 15,
            "active_jobs": 15,
            "success_rate": 80.0,
            "total_businesses_found": 450
        },
        "status_breakdown": {
            "pending": 5,
            "running": 3,
            "completed": 120,
            "failed": 15,
            "cancelled": 7
        },
        "recent_jobs": [ /* Array of recent crawler jobs */ ]
    }
}
```

### **Bulk Actions on Crawler Jobs**

```http
POST /api/business-crawler-jobs/bulk-action
Authorization: Bearer {token} (Admin only)
Content-Type: application/json

{
    "action": "pause", // pause, resume, cancel, retry
    "crawler_job_ids": [123, 124, 125],
    "reason": "System maintenance" // required for pause/cancel
}

Response:
{
    "success": true,
    "message": "Bulk pause completed. 3 of 3 jobs processed successfully.",
    "data": {
        "action": "pause",
        "total_jobs": 3,
        "successful_jobs": 3,
        "results": [
            {
                "crawler_job_id": 123,
                "success": true,
                "message": "Paused successfully"
            }
        ]
    }
}
```

---

_This endpoint documentation provides comprehensive coverage of all API endpoints available in the JobON application. Each endpoint includes request/response examples and required parameters._
