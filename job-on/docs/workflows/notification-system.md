# Job Notification System Workflow

## 🎯 Overview

The Job Notification System is a critical component that connects job postings with qualified service providers through automated email campaigns and approval workflows.

## 🔄 Complete Notification Workflow

```mermaid
graph TD
    A[Job Booking Created] --> B[Notification Campaign Created]
    B --> C{Auto-Approval Enabled?}
    C -->|Yes| D[Campaign Auto-Approved]
    C -->|No| E[Admin Approval Required]
    E --> F[Admin Email Sent]
    F --> G[Admin Reviews Campaign]
    G --> H{Admin Decision}
    H -->|Approve| I[Campaign Approved]
    H -->|Reject| J[Campaign Rejected]
    D --> K[Find Eligible Providers]
    I --> K
    K --> L{Providers Found?}
    L -->|Yes| M[Create Recipient Records]
    L -->|No| N{Crawler Enabled?}
    N -->|Yes| O[Create Crawler Job]
    N -->|No| P[Mark Campaign Rejected]
    O --> Q[Background Business Search]
    Q --> R{Businesses Found?}
    R -->|Yes| S[Create Recipients & Send Notifications]
    R -->|No| T{Max Attempts Reached?}
    T -->|No| U[Expand Radius & Retry]
    T -->|Yes| V[Mark Crawler Failed]
    U --> Q
    M --> W[Queue Email Jobs]
    S --> W
    W --> X[Send Provider Emails]
    X --> Y[Track Email Delivery]
    Y --> Z[Providers Receive Notifications]
    Z --> AA[Providers Submit Bids]
    J --> BB[Campaign Ended]
    P --> BB
    V --> BB
    AA --> CC[Customer Reviews Bids]
```

## 📋 System Components

### **Core Models**

#### **JobNotificationCampaign**

```php
class JobNotificationCampaign extends Model
{
    protected $fillable = [
        'job_id',
        'job_title',
        'job_description',
        'job_budget',
        'job_zip_code',
        'search_radius',
        'business_count',
        'status',
        'admin_token',
        'token_expires_at',
        'approved_at',
        'approved_by',
        'rejected_at',
        'rejected_by',
        'rejection_reason'
    ];

    protected $casts = [
        'token_expires_at' => 'datetime',
        'approved_at' => 'datetime',
        'rejected_at' => 'datetime',
    ];
}
```

**Status Values:**

- `pending_approval` - Waiting for admin approval
- `approved` - Approved and ready for sending
- `sent` - Notifications sent to providers
- `rejected` - Rejected by admin
- `expired` - Approval token expired

#### **JobNotificationRecipient**

```php
class JobNotificationRecipient extends Model
{
    protected $fillable = [
        'campaign_id',
        'provider_id',
        'email',
        'status',
        'sent_at',
        'opened_at',
        'clicked_at',
        'bid_submitted_at'
    ];

    protected $casts = [
        'sent_at' => 'datetime',
        'opened_at' => 'datetime',
        'clicked_at' => 'datetime',
        'bid_submitted_at' => 'datetime',
    ];
}
```

**Status Values:**

- `pending` - Ready to send
- `sent` - Email sent successfully
- `failed` - Email delivery failed
- `opened` - Email opened by provider
- `clicked` - Provider clicked bid link
- `bid_submitted` - Provider submitted bid

## 🚀 Campaign Creation Process

### **Step 1: Job Booking Event**

```php
// Event: JobBookingCreated
class JobBookingCreated
{
    public function __construct(
        public JobBooking $jobBooking
    ) {}
}

// Listener: CreateNotificationCampaign
class CreateNotificationCampaign
{
    public function handle(JobBookingCreated $event): void
    {
        $campaign = $this->campaignService->createCampaign($event->jobBooking);

        if (config('job_notification.auto_approve')) {
            $this->campaignService->approveCampaign($campaign);
        } else {
            $this->notificationService->sendAdminApprovalEmail($campaign);
        }
    }
}
```

### **Step 2: Campaign Creation Logic**

```php
class CampaignService
{
    public function createCampaign(JobBooking $jobBooking): JobNotificationCampaign
    {
        return JobNotificationCampaign::create([
            'job_id' => $jobBooking->id,
            'job_title' => $this->generateJobTitle($jobBooking),
            'job_description' => $this->formatJobDescription($jobBooking),
            'job_budget' => $this->estimateJobBudget($jobBooking),
            'job_zip_code' => $jobBooking->zip_code,
            'search_radius' => config('job_notification.default_radius', 25),
            'status' => 'pending_approval',
            'admin_token' => Str::random(64),
            'token_expires_at' => now()->addHours(24),
        ]);
    }

    private function generateJobTitle(JobBooking $jobBooking): string
    {
        $category = ucfirst($jobBooking->service_category);
        $property = ucfirst($jobBooking->property_type);

        return "{$category} Services Needed - {$property} Property";
    }

    private function estimateJobBudget(JobBooking $jobBooking): string
    {
        // Logic to estimate budget based on service category and tasks
        $basePrices = config('job_notification.base_prices');
        $categoryPrice = $basePrices[$jobBooking->service_category] ?? 100;
        $taskMultiplier = count($jobBooking->service_tasks);

        $estimatedMin = $categoryPrice * $taskMultiplier * 0.8;
        $estimatedMax = $categoryPrice * $taskMultiplier * 1.5;

        return "$" . number_format($estimatedMin) . " - $" . number_format($estimatedMax);
    }
}
```

## 🔐 Admin Approval Workflow

### **Approval Email Template**

```php
// Mail: AdminCampaignApprovalMail
class AdminCampaignApprovalMail extends Mailable
{
    public function __construct(
        public JobNotificationCampaign $campaign
    ) {}

    public function build(): self
    {
        $approveUrl = route('admin.campaigns.approve', [
            'campaign' => $this->campaign->id,
            'token' => $this->campaign->admin_token
        ]);

        $rejectUrl = route('admin.campaigns.reject', [
            'campaign' => $this->campaign->id,
            'token' => $this->campaign->admin_token
        ]);

        return $this->subject('Job Notification Campaign Approval Required')
                    ->view('emails.admin.campaign-approval')
                    ->with([
                        'campaign' => $this->campaign,
                        'approveUrl' => $approveUrl,
                        'rejectUrl' => $rejectUrl,
                    ]);
    }
}
```

### **Approval Controller**

```php
class CampaignApprovalController extends Controller
{
    public function approve(Request $request, JobNotificationCampaign $campaign)
    {
        // Validate token and expiration
        if (!$this->isValidToken($campaign, $request->token)) {
            abort(403, 'Invalid or expired approval token');
        }

        // Approve campaign
        $campaign->update([
            'status' => 'approved',
            'approved_at' => now(),
            'approved_by' => 'admin', // Could be specific admin user
        ]);

        // Trigger provider notification process
        ProcessCampaignApproval::dispatch($campaign);

        return view('admin.campaign-approved', compact('campaign'));
    }

    public function reject(Request $request, JobNotificationCampaign $campaign)
    {
        $request->validate([
            'reason' => 'required|string|max:500'
        ]);

        if (!$this->isValidToken($campaign, $request->token)) {
            abort(403, 'Invalid or expired approval token');
        }

        $campaign->update([
            'status' => 'rejected',
            'rejected_at' => now(),
            'rejected_by' => 'admin',
            'rejection_reason' => $request->reason,
        ]);

        return view('admin.campaign-rejected', compact('campaign'));
    }
}
```

## 🎯 Provider Discovery & Targeting

### **Provider Matching Algorithm**

```php
class ProviderMatchingService
{
    public function findEligibleProviders(JobNotificationCampaign $campaign): Collection
    {
        $jobBooking = $campaign->jobBooking;

        return User::role('provider')
            ->where('status', 'active')
            ->where('is_verified', true)
            ->whereHas('expertise', function ($query) use ($jobBooking) {
                $query->where('service_category', $jobBooking->service_category);
            })
            ->whereRaw($this->getLocationQuery(), [
                $jobBooking->longitude,
                $jobBooking->latitude,
                $campaign->search_radius * 1609.34 // Convert miles to meters
            ])
            ->whereDoesntHave('bids', function ($query) use ($jobBooking) {
                $query->where('job_booking_id', $jobBooking->id);
            })
            ->limit(config('job_notification.max_recipients', 100))
            ->get();
    }

    private function getLocationQuery(): string
    {
        return "ST_Distance_Sphere(
            POINT(longitude, latitude),
            POINT(?, ?)
        ) <= ?";
    }
}
```

### **Recipient Record Creation**

```php
class RecipientService
{
    public function createRecipients(
        JobNotificationCampaign $campaign,
        Collection $providers
    ): void {
        $recipients = $providers->map(function ($provider) use ($campaign) {
            return [
                'campaign_id' => $campaign->id,
                'provider_id' => $provider->id,
                'email' => $provider->email,
                'status' => 'pending',
                'created_at' => now(),
                'updated_at' => now(),
            ];
        });

        JobNotificationRecipient::insert($recipients->toArray());

        // Update campaign with recipient count
        $campaign->update([
            'business_count' => $recipients->count()
        ]);
    }
}
```

## 📧 Email Notification Process

### **Email Queue Job**

```php
class SendJobNotificationEmail implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public function __construct(
        public JobNotificationRecipient $recipient
    ) {}

    public function handle(): void
    {
        try {
            Mail::to($this->recipient->email)
                ->send(new JobNotificationMail($this->recipient));

            $this->recipient->update([
                'status' => 'sent',
                'sent_at' => now(),
            ]);

        } catch (Exception $e) {
            $this->recipient->update([
                'status' => 'failed'
            ]);

            Log::error('Job notification email failed', [
                'recipient_id' => $this->recipient->id,
                'error' => $e->getMessage()
            ]);

            throw $e; // Re-throw for queue retry logic
        }
    }
}
```

### **Provider Email Template**

```php
class JobNotificationMail extends Mailable
{
    public function __construct(
        public JobNotificationRecipient $recipient
    ) {}

    public function build(): self
    {
        $campaign = $this->recipient->campaign;
        $jobBooking = $campaign->jobBooking;

        $bidUrl = route('provider.bid.create', [
            'job' => $jobBooking->job_uuid,
            'token' => $this->generateBidToken($this->recipient)
        ]);

        return $this->subject("New Job Opportunity: {$campaign->job_title}")
                    ->view('emails.provider.job-notification')
                    ->with([
                        'campaign' => $campaign,
                        'jobBooking' => $jobBooking,
                        'provider' => $this->recipient->provider,
                        'bidUrl' => $bidUrl,
                        'trackingPixel' => $this->generateTrackingPixel(),
                    ]);
    }

    private function generateBidToken(JobNotificationRecipient $recipient): string
    {
        return encrypt([
            'recipient_id' => $recipient->id,
            'provider_id' => $recipient->provider_id,
            'expires_at' => now()->addDays(7)->timestamp,
        ]);
    }
}
```

## 📊 Tracking & Analytics

### **Email Tracking**

```php
// Email open tracking
Route::get('/track/email/{recipient}', function (JobNotificationRecipient $recipient) {
    $recipient->update([
        'status' => 'opened',
        'opened_at' => now(),
    ]);

    // Return 1x1 transparent pixel
    return response(base64_decode('R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7'))
           ->header('Content-Type', 'image/gif');
});

// Link click tracking
Route::get('/track/click/{recipient}', function (JobNotificationRecipient $recipient) {
    $recipient->update([
        'status' => 'clicked',
        'clicked_at' => now(),
    ]);

    return redirect()->route('provider.bid.create', [
        'job' => $recipient->campaign->jobBooking->job_uuid
    ]);
});
```

### **Campaign Analytics**

```php
class CampaignAnalyticsService
{
    public function getCampaignStats(JobNotificationCampaign $campaign): array
    {
        $recipients = $campaign->recipients();

        return [
            'total_sent' => $recipients->where('status', '!=', 'pending')->count(),
            'delivery_rate' => $this->calculateDeliveryRate($recipients),
            'open_rate' => $this->calculateOpenRate($recipients),
            'click_rate' => $this->calculateClickRate($recipients),
            'bid_conversion_rate' => $this->calculateBidConversionRate($recipients),
            'total_bids' => $campaign->jobBooking->bids()->count(),
        ];
    }

    private function calculateDeliveryRate(Collection $recipients): float
    {
        $sent = $recipients->whereIn('status', ['sent', 'opened', 'clicked'])->count();
        $total = $recipients->where('status', '!=', 'pending')->count();

        return $total > 0 ? ($sent / $total) * 100 : 0;
    }
}
```

## ⚙️ Configuration

### **Notification Settings**

```php
// config/job_notification.php
return [
    'auto_approve' => env('JOB_NOTIFICATION_AUTO_APPROVE', false),
    'default_radius' => env('JOB_NOTIFICATION_RADIUS', 25), // miles
    'max_recipients' => env('JOB_NOTIFICATION_MAX_RECIPIENTS', 100),
    'approval_token_expiry' => env('JOB_NOTIFICATION_TOKEN_EXPIRY', 24), // hours

    'base_prices' => [
        'cleaning' => 100,
        'plumbing' => 150,
        'electrical' => 200,
        'painting' => 120,
        'landscaping' => 80,
    ],

    'email_settings' => [
        'from_name' => env('JOB_NOTIFICATION_FROM_NAME', 'JobON'),
        'from_email' => env('JOB_NOTIFICATION_FROM_EMAIL', '<EMAIL>'),
        'track_opens' => env('JOB_NOTIFICATION_TRACK_OPENS', true),
        'track_clicks' => env('JOB_NOTIFICATION_TRACK_CLICKS', true),
    ],
];
```

---

## 🤖 Business Crawler System

### **Overview**

When no businesses are initially found for a job notification campaign, the Business Crawler system automatically attempts to find businesses through:

- Expanded search radius
- Periodic retries to catch newly added businesses
- Background processing to avoid blocking the main workflow

### **Crawler Job Lifecycle**

1. **Creation** - Created when ProcessJobNotificationJob finds no businesses
2. **Background Processing** - Runs in dedicated queue with exponential backoff
3. **Radius Expansion** - Gradually expands search radius up to configured limit
4. **Retry Logic** - Configurable attempts with smart delays
5. **Completion** - Either finds businesses or reaches maximum attempts

### **API Endpoints**

```http
GET /api/business-crawler-jobs          # List crawler jobs
GET /api/business-crawler-jobs/{id}     # Get specific crawler job
POST /api/business-crawler-jobs/{id}/pause   # Pause crawler job
POST /api/business-crawler-jobs/{id}/resume  # Resume crawler job
POST /api/business-crawler-jobs/{id}/cancel  # Cancel crawler job
POST /api/business-crawler-jobs/{id}/retry   # Retry failed job
GET /api/business-crawler-jobs/statistics    # Get crawler statistics
POST /api/business-crawler-jobs/bulk-action  # Bulk operations
```

### **Configuration**

```php
// config/job_notification.php
'business_crawler' => [
    'enabled' => true,
    'max_attempts' => 10,
    'retry_delays' => [60, 300, 900, 1800, 3600], // seconds
    'max_radius_multiplier' => 3.0,
    'radius_expansion_factor' => 1.25,
    'notify_admin_on_failure' => true,
    'queue_name' => 'business-crawler',
],
```

### **Management Commands**

```bash
# Show crawler status and statistics
php artisan crawler:status

# Show detailed information
php artisan crawler:status --detailed

# Filter by status
php artisan crawler:status --status=failed
```

---

_The Job Notification System with Business Crawler ensures comprehensive business discovery through automated campaigns, background crawling, and intelligent retry mechanisms._
