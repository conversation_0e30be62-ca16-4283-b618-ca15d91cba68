# Job Booking Workflow Documentation

## 🎯 Overview

The Job Booking workflow is the core business process of the JobON platform, enabling customers to post job requirements and receive bids from qualified service providers.

## 🔄 Complete Workflow Diagram

```mermaid
graph TD
    A[Customer Creates Job Booking] --> B[Job Booking Validation]
    B --> C[Job Booking Saved]
    C --> D[Notification Campaign Created]
    D --> E[Find Eligible Providers]
    E --> F[Send Email Notifications]
    F --> G[Providers View Job Details]
    G --> H[Providers Submit Bids]
    H --> I[Customer Reviews Bids]
    I --> J{Customer Decision}
    J -->|Accept| K[Bid Accepted]
    J -->|Reject| L[Bid Rejected]
    K --> M[Job Created]
    M --> N[Provider Notified]
    N --> O[Job Execution]
    O --> P[Job Completion]
    P --> Q[Review & Rating]
    L --> R[Provider Notified]
    R --> S[End]
    Q --> S
```

## 📋 Step-by-Step Process

### **Step 1: Job Booking Creation**

**Trigger**: Customer submits job booking form
**Endpoint**: `POST /api/job-bookings`

**Process**:
1. Customer fills out job booking form with:
   - Job type (send_bids/find_providers)
   - Property type (residential/commercial/industrial)
   - Service category and specific tasks
   - Location details (address, city, state, zip)
   - Schedule preferences
   - Contact information

2. System validates input data:
   - Required fields validation
   - Location validation
   - Date validation (future dates only)
   - Service category validation

3. `JobBooking` model created with status "pending"

**Code Flow**:
```php
// JobBookingController@store
$jobBooking = JobBooking::create([
    'job_uuid' => Str::uuid(),
    'project_code' => $this->generateProjectCode(),
    'job_type' => $request->job_type,
    'property_type' => $request->property_type,
    'service_category' => $request->service_category,
    'service_tasks' => $request->service_tasks,
    'status' => JobBookingStatusEnum::PENDING,
    'user_id' => auth()->id(),
    // ... other fields
]);
```

### **Step 2: Notification Campaign Setup**

**Trigger**: Job booking creation event
**Handler**: `JobBookingCreated` event listener

**Process**:
1. `JobNotificationCampaign` created for the job
2. Admin approval token generated
3. Campaign status set to "pending_approval"

**Code Flow**:
```php
// JobNotificationCampaign creation
$campaign = JobNotificationCampaign::create([
    'job_id' => $jobBooking->id,
    'job_title' => $this->generateJobTitle($jobBooking),
    'job_description' => $jobBooking->description,
    'job_budget' => $this->estimateBudget($jobBooking),
    'job_zip_code' => $jobBooking->zip_code,
    'search_radius' => config('job_notification.default_radius'),
    'status' => 'pending_approval',
    'admin_token' => Str::random(64),
    'token_expires_at' => now()->addHours(24),
]);
```

### **Step 3: Provider Discovery**

**Trigger**: Campaign approval (manual or automatic)
**Service**: `ProviderMatchingService`

**Process**:
1. Find providers within search radius
2. Filter by service category expertise
3. Check provider availability and status
4. Create `JobNotificationRecipient` records

**Algorithm**:
```php
// Provider matching logic
$providers = User::role('provider')
    ->where('status', 'active')
    ->whereHas('expertise', function($query) use ($serviceCategory) {
        $query->where('service_category', $serviceCategory);
    })
    ->whereRaw("
        ST_Distance_Sphere(
            POINT(?, ?), 
            POINT(longitude, latitude)
        ) <= ?
    ", [$jobLng, $jobLat, $radiusInMeters])
    ->get();
```

### **Step 4: Email Notification Dispatch**

**Trigger**: Provider discovery completion
**Queue**: `SendJobNotificationEmail` job

**Process**:
1. Generate personalized email content
2. Include job details and direct bid link
3. Track email delivery status
4. Update recipient records

**Email Content**:
- Job title and description
- Location and schedule details
- Estimated budget range
- Direct link to bid submission
- Customer contact preferences

### **Step 5: Bid Submission**

**Trigger**: Provider clicks bid link from email
**Endpoint**: `POST /api/bids`

**Process**:
1. Provider reviews job details
2. Submits bid with:
   - Proposed amount
   - Service description
   - Estimated completion time
   - Additional notes

3. `Bid` model created with status "requested"
4. Customer notification sent

**Validation Rules**:
```php
// Bid validation
$rules = [
    'job_booking_id' => 'required|exists:job_bookings,id',
    'amount' => 'required|numeric|min:1|max:10000',
    'description' => 'required|string|max:1000',
    'estimated_completion_time' => 'required|date|after:now',
];
```

### **Step 6: Bid Review & Decision**

**Trigger**: Customer reviews submitted bids
**Endpoint**: `PATCH /api/bids/{id}/status`

**Process**:
1. Customer views all submitted bids
2. Compares provider profiles and ratings
3. Makes acceptance/rejection decision
4. System updates bid status

**Business Rules**:
- Only one bid can be accepted per job booking
- Rejected bids cannot be re-accepted
- Accepted bids automatically create jobs

### **Step 7: Job Creation**

**Trigger**: Bid acceptance
**Event**: `BidAccepted` event

**Process**:
1. `Job` model created from accepted bid
2. Job booking status updated to "assigned"
3. Other bids automatically rejected
4. Provider and customer notifications sent

**Code Flow**:
```php
// Job creation from accepted bid
$job = Job::create([
    'job_uuid' => Str::uuid(),
    'job_booking_id' => $bid->job_booking_id,
    'bid_id' => $bid->id,
    'customer_id' => $jobBooking->user_id,
    'provider_id' => $bid->provider_id,
    'status' => JobStatusEnum::ASSIGNED,
    'agreed_amount' => $bid->amount,
    'estimated_completion_time' => $bid->estimated_completion_time,
]);
```

### **Step 8: Job Execution**

**Trigger**: Job assignment
**Actors**: Provider and Customer

**Process**:
1. Provider confirms job acceptance
2. Job status progresses through:
   - `assigned` → `in_progress` → `completed`
3. Real-time status updates via WebSocket
4. Customer can track progress

**Status Transitions**:
```php
// Allowed status transitions
$allowedTransitions = [
    'assigned' => ['in_progress', 'cancelled'],
    'in_progress' => ['completed', 'on_hold'],
    'on_hold' => ['in_progress', 'cancelled'],
    'completed' => ['review_pending'],
];
```

### **Step 9: Completion & Review**

**Trigger**: Job marked as completed
**Process**:
1. Provider marks job as completed
2. Customer confirms completion
3. Payment processing initiated
4. Review and rating system activated

## 🎯 Key Business Rules

### **Job Booking Rules**
- Schedule date must be at least 24 hours in future
- Service tasks must be valid for selected category
- Location must be within serviceable areas
- Contact information is required

### **Bid Rules**
- Providers can only bid once per job
- Bid amount must be reasonable (within 50-200% of estimated range)
- Bids expire after job booking deadline
- Providers must have verified profiles to bid

### **Job Rules**
- Only accepted bids create jobs
- Jobs cannot be cancelled after work begins
- Payment is held in escrow until completion
- Both parties can communicate through platform

## 📊 Status Enums

### **JobBookingStatusEnum**
- `pending` - Initial status
- `open` - Available for bidding
- `assigned` - Bid accepted, job created
- `completed` - Job finished
- `cancelled` - Job cancelled

### **BidStatusEnum**
- `requested` - Initial bid submission
- `accepted` - Bid accepted by customer
- `rejected` - Bid rejected by customer

### **JobStatusEnum**
- `assigned` - Job assigned to provider
- `in_progress` - Work in progress
- `on_hold` - Temporarily paused
- `completed` - Work completed
- `cancelled` - Job cancelled

## 🔔 Notification Points

1. **Job Booking Created** → Admin notification
2. **Campaign Approved** → Provider notifications
3. **Bid Submitted** → Customer notification
4. **Bid Accepted/Rejected** → Provider notification
5. **Job Created** → Both parties notification
6. **Status Updates** → Real-time updates
7. **Job Completed** → Payment and review prompts

## 📈 Analytics & Tracking

### **Campaign Metrics**
- Provider reach and response rates
- Email open and click rates
- Bid submission rates
- Conversion rates

### **Job Metrics**
- Time to first bid
- Average number of bids per job
- Acceptance rates
- Completion rates
- Customer satisfaction scores

---

*This workflow documentation provides the complete picture of how jobs flow through the JobON platform from initial posting to final completion.*
