# Development Setup Guide

## 🎯 Prerequisites

### **System Requirements**
- PHP 8.1 or higher
- Composer 2.0+
- Node.js 16+ and npm/yarn
- MySQL 8.0+ with spatial extensions
- Redis (for caching and queues)
- Git

### **Recommended Tools**
- <PERSON>er & Docker Compose (for containerized development)
- <PERSON><PERSON> (for macOS local development)
- PHPStorm or VS Code with PHP extensions

## 🚀 Installation Steps

### **1. Clone Repository**
```bash
git clone https://github.com/your-org/jobon.git
cd jobon/job-on
```

### **2. Install Dependencies**
```bash
# Install PHP dependencies
composer install

# Install Node.js dependencies
npm install
# or
yarn install
```

### **3. Environment Configuration**
```bash
# Copy environment file
cp .env.example .env

# Generate application key
php artisan key:generate

# Generate JWT secret
php artisan jwt:secret
```

### **4. Database Setup**
```bash
# Create database
mysql -u root -p -e "CREATE DATABASE jobon_dev CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"

# Run migrations
php artisan migrate

# Seed database with initial data
php artisan db:seed
```

### **5. Storage & Permissions**
```bash
# Create storage link
php artisan storage:link

# Set proper permissions (Linux/macOS)
chmod -R 775 storage bootstrap/cache
```

### **6. Module Setup**
```bash
# Publish module assets
php artisan module:publish

# Run module migrations
php artisan module:migrate
```

## 🔧 Environment Configuration

### **Database Configuration**
```env
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=jobon_dev
DB_USERNAME=your_username
DB_PASSWORD=your_password
```

### **Redis Configuration**
```env
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379
REDIS_DB=0
```

### **Mail Configuration**
```env
MAIL_MAILER=smtp
MAIL_HOST=mailhog
MAIL_PORT=1025
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"
```

### **Queue Configuration**
```env
QUEUE_CONNECTION=redis
```

### **JWT Configuration**
```env
JWT_SECRET=your_jwt_secret_here
JWT_TTL=60
JWT_REFRESH_TTL=20160
```

### **Payment Gateway Configuration**
```env
# Stripe
STRIPE_KEY=pk_test_your_stripe_key
STRIPE_SECRET=sk_test_your_stripe_secret
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret

# PayPal
PAYPAL_CLIENT_ID=your_paypal_client_id
PAYPAL_SECRET=your_paypal_secret
PAYPAL_MODE=sandbox

# RazorPay
RAZORPAY_KEY=your_razorpay_key
RAZORPAY_SECRET=your_razorpay_secret
```

### **SMS Configuration**
```env
# Twilio
TWILIO_SID=your_twilio_sid
TWILIO_TOKEN=your_twilio_token
TWILIO_FROM=your_twilio_phone

# Nexmo
NEXMO_KEY=your_nexmo_key
NEXMO_SECRET=your_nexmo_secret
NEXMO_FROM=your_nexmo_from
```

### **Firebase Configuration**
```env
FIREBASE_SERVER_KEY=your_firebase_server_key
FIREBASE_PROJECT_ID=your_firebase_project_id
```

## 🐳 Docker Development Setup

### **Using Docker Compose**
```bash
# Start all services
docker-compose up -d

# Install dependencies inside container
docker-compose exec app composer install
docker-compose exec app npm install

# Run migrations
docker-compose exec app php artisan migrate --seed

# Access application
# Web: http://localhost:8000
# API: http://localhost:8000/api
# MailHog: http://localhost:8025
# phpMyAdmin: http://localhost:8080
```

### **Docker Compose Services**
```yaml
# docker-compose.yml
version: '3.8'
services:
  app:
    build: .
    ports:
      - "8000:8000"
    volumes:
      - .:/var/www/html
    depends_on:
      - mysql
      - redis

  mysql:
    image: mysql:8.0
    environment:
      MYSQL_DATABASE: jobon_dev
      MYSQL_ROOT_PASSWORD: secret
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql

  redis:
    image: redis:alpine
    ports:
      - "6379:6379"

  mailhog:
    image: mailhog/mailhog
    ports:
      - "1025:1025"
      - "8025:8025"

volumes:
  mysql_data:
```

## 🔄 Development Workflow

### **Daily Development Commands**
```bash
# Start development server
php artisan serve

# Watch and compile assets
npm run dev
# or for production build
npm run build

# Run queue worker
php artisan queue:work

# Run WebSocket server
php websocket-server.php

# Clear caches
php artisan cache:clear
php artisan config:clear
php artisan route:clear
php artisan view:clear
```

### **Database Operations**
```bash
# Fresh migration with seeding
php artisan migrate:fresh --seed

# Create new migration
php artisan make:migration create_table_name

# Create model with migration and factory
php artisan make:model ModelName -mf

# Run specific seeder
php artisan db:seed --class=SpecificSeeder
```

### **Module Development**
```bash
# Create new module
php artisan module:make ModuleName

# Generate module components
php artisan module:make-controller ModuleName ControllerName
php artisan module:make-model ModuleName ModelName
php artisan module:make-migration ModuleName migration_name

# Enable/disable modules
php artisan module:enable ModuleName
php artisan module:disable ModuleName
```

## 🧪 Testing Setup

### **PHPUnit Configuration**
```bash
# Copy test environment
cp .env.testing.example .env.testing

# Create test database
mysql -u root -p -e "CREATE DATABASE jobon_test CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"

# Run tests
php artisan test

# Run specific test
php artisan test --filter=JobBookingTest

# Run with coverage
php artisan test --coverage
```

### **Test Database Setup**
```env
# .env.testing
DB_CONNECTION=mysql
DB_DATABASE=jobon_test
DB_USERNAME=your_username
DB_PASSWORD=your_password

QUEUE_CONNECTION=sync
MAIL_MAILER=array
```

## 🔧 IDE Configuration

### **VS Code Extensions**
- PHP Intelephense
- Laravel Extension Pack
- Laravel Blade Snippets
- Laravel goto view
- GitLens

### **VS Code Settings**
```json
{
    "php.suggest.basic": false,
    "php.validate.executablePath": "/usr/bin/php",
    "emmet.includeLanguages": {
        "blade": "html"
    },
    "files.associations": {
        "*.blade.php": "blade"
    }
}
```

### **PHPStorm Configuration**
1. Enable Laravel plugin
2. Set PHP interpreter to 8.1+
3. Configure database connection
4. Set up code style to PSR-12
5. Configure Xdebug for debugging

## 🐛 Debugging Setup

### **Xdebug Configuration**
```ini
; php.ini
[xdebug]
xdebug.mode=debug
xdebug.start_with_request=yes
xdebug.client_host=127.0.0.1
xdebug.client_port=9003
```

### **Laravel Telescope**
```bash
# Install Telescope (if not already installed)
composer require laravel/telescope --dev

# Publish Telescope assets
php artisan telescope:install

# Migrate Telescope tables
php artisan migrate

# Access Telescope dashboard
# http://localhost:8000/telescope
```

### **Debug Tools**
```php
// Use Laravel's built-in debugging
dd($variable); // Dump and die
dump($variable); // Dump without stopping

// Log debugging
Log::debug('Debug message', ['data' => $variable]);

// Query debugging
DB::enableQueryLog();
// ... run queries
dd(DB::getQueryLog());
```

## 📊 Performance Monitoring

### **Laravel Debugbar**
```bash
# Install debugbar
composer require barryvdh/laravel-debugbar --dev

# Publish config
php artisan vendor:publish --provider="Barryvdh\Debugbar\ServiceProvider"
```

### **Monitoring Tools**
- Laravel Telescope for request monitoring
- Laravel Debugbar for query analysis
- Redis CLI for cache monitoring
- MySQL slow query log for database optimization

## 🚀 Production Deployment Preparation

### **Optimization Commands**
```bash
# Optimize for production
php artisan config:cache
php artisan route:cache
php artisan view:cache
php artisan event:cache

# Build production assets
npm run build

# Clear development caches
php artisan cache:clear
php artisan config:clear
```

### **Security Checklist**
- [ ] Update all dependencies
- [ ] Set proper file permissions
- [ ] Configure HTTPS
- [ ] Set secure environment variables
- [ ] Enable CSRF protection
- [ ] Configure rate limiting
- [ ] Set up proper backup strategy

---

*This setup guide provides everything needed to get the JobON application running in a development environment. Follow the steps in order for a smooth setup experience.*
