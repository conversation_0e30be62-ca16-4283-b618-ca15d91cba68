<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('business_crawler_jobs', function (Blueprint $table) {
            $table->id();
            
            // Reference to the original campaign
            $table->foreignId('campaign_id')
                  ->constrained('job_notification_campaigns')
                  ->onDelete('cascade');
            
            // Store original job data from webhook
            $table->json('job_data');
            
            // Current search criteria (radius, location, category, etc.)
            $table->json('search_criteria');
            
            // Job status
            $table->enum('status', [
                'pending', 
                'running', 
                'paused', 
                'completed', 
                'failed', 
                'cancelled'
            ])->default('pending');
            
            // Retry logic fields
            $table->unsignedInteger('attempts')->default(0);
            $table->unsignedInteger('max_attempts')->default(10);
            
            // Search radius tracking
            $table->decimal('current_radius', 8, 2)->nullable();
            $table->decimal('original_radius', 8, 2)->nullable();
            $table->decimal('max_radius', 8, 2)->nullable();
            
            // Scheduling
            $table->timestamp('next_run_at')->nullable();
            $table->timestamp('last_run_at')->nullable();
            
            // Progress tracking
            $table->unsignedInteger('businesses_found')->default(0);
            $table->unsignedInteger('total_searches_performed')->default(0);
            
            // Error handling
            $table->text('last_error')->nullable();
            $table->json('search_history')->nullable(); // Track previous searches
            
            // Completion tracking
            $table->timestamp('completed_at')->nullable();
            $table->timestamp('cancelled_at')->nullable();
            $table->string('cancelled_by')->nullable();
            $table->text('cancellation_reason')->nullable();
            
            $table->timestamps();
            
            // Indexes for performance
            $table->index(['status', 'next_run_at']);
            $table->index(['campaign_id']);
            $table->index(['created_at']);
            $table->index(['status', 'created_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('business_crawler_jobs');
    }
}; 